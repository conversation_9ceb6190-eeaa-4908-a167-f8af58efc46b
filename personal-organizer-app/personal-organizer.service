[Unit]
Description=Personal Organizer Web Application
Documentation=https://github.com/your-username/personal-organizer
After=network.target mongodb.service
Wants=mongodb.service

[Service]
Type=exec
User=www-data
Group=www-data
WorkingDirectory=/opt/personal-organizer
Environment=PATH=/opt/personal-organizer/venv/bin
ExecStart=/opt/personal-organizer/venv/bin/gunicorn --config /opt/personal-organizer/gunicorn.conf.py wsgi:application
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
Restart=on-failure
RestartSec=5
StartLimitInterval=60s
StartLimitBurst=3

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/personal-organizer/logs
ReadWritePaths=/var/log/personal-organizer
PrivateDevices=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictSUIDSGID=true
LockPersonality=true
MemoryDenyWriteExecute=true
RestrictNamespaces=true
SystemCallFilter=@system-service
SystemCallErrorNumber=EPERM

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Environment variables
Environment=FLASK_ENV=production
Environment=PYTHONPATH=/opt/personal-organizer
Environment=PYTHONUNBUFFERED=1

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=personal-organizer

[Install]
WantedBy=multi-user.target
