const { test, expect } = require('@playwright/test');

test.describe('Login Page Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
  });

  test('should display login page with all elements', async ({ page }) => {
    // Take screenshot of login page
    await page.screenshot({ path: 'test-results/artifacts/01-login-page-initial.png', fullPage: true });
    
    // Check page title
    await expect(page).toHaveTitle(/Personal Organizer/);
    
    // Check main heading
    await expect(page.locator('h1')).toContainText('Personal Organizer');
    await expect(page.locator('h2')).toContainText('Welcome Back');
    
    // Check form elements exist
    await expect(page.locator('input[name="username"]')).toBeVisible();
    await expect(page.locator('input[name="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
    
    // Check labels
    await expect(page.locator('label[for="username"]')).toContainText('Username');
    await expect(page.locator('label[for="password"]')).toContainText('Password');
    
    // Check sign in button
    await expect(page.locator('button[type="submit"]')).toContainText('Sign in');
    
    console.log('✅ Login page elements verified');
  });

  test('should show password toggle functionality', async ({ page }) => {
    const passwordInput = page.locator('input[name="password"]');
    const toggleButton = page.locator('button[type="button"]').filter({ hasText: /eye/i }).first();
    
    // Initially password should be hidden
    await expect(passwordInput).toHaveAttribute('type', 'password');
    
    // Click toggle to show password
    await toggleButton.click();
    await page.screenshot({ path: 'test-results/artifacts/01-login-password-visible.png', fullPage: true });
    
    // Password should now be visible
    await expect(passwordInput).toHaveAttribute('type', 'text');
    
    // Click toggle to hide password again
    await toggleButton.click();
    await expect(passwordInput).toHaveAttribute('type', 'password');
    
    console.log('✅ Password toggle functionality verified');
  });

  test('should show validation errors for empty fields', async ({ page }) => {
    // Try to submit empty form
    await page.locator('button[type="submit"]').click();
    
    // Take screenshot of validation errors
    await page.screenshot({ path: 'test-results/artifacts/01-login-validation-errors.png', fullPage: true });
    
    // Check for validation messages
    await expect(page.locator('text=Username is required')).toBeVisible();
    await expect(page.locator('text=Password is required')).toBeVisible();
    
    console.log('✅ Form validation verified');
  });

  test('should show error for invalid credentials', async ({ page }) => {
    // Fill in invalid credentials
    await page.locator('input[name="username"]').fill('invaliduser');
    await page.locator('input[name="password"]').fill('wrongpassword');
    
    // Submit form
    await page.locator('button[type="submit"]').click();
    
    // Wait for error message
    await page.waitForSelector('text=Invalid username or password', { timeout: 10000 });
    
    // Take screenshot of error
    await page.screenshot({ path: 'test-results/artifacts/01-login-invalid-credentials.png', fullPage: true });
    
    // Verify error message
    await expect(page.locator('text=Invalid username or password')).toBeVisible();
    
    console.log('✅ Invalid credentials error verified');
  });

  test('should handle loading state during login', async ({ page }) => {
    // Fill in credentials (even if invalid)
    await page.locator('input[name="username"]').fill('testuser');
    await page.locator('input[name="password"]').fill('testpass');
    
    // Submit form and immediately check loading state
    await page.locator('button[type="submit"]').click();
    
    // Take screenshot during loading
    await page.screenshot({ path: 'test-results/artifacts/01-login-loading-state.png', fullPage: true });
    
    // Check for loading indicator
    const loadingButton = page.locator('button[type="submit"]');
    await expect(loadingButton).toContainText(/Signing in/);
    
    console.log('✅ Loading state verified');
  });

  test('should be responsive on mobile viewport', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    
    // Take screenshot of mobile view
    await page.screenshot({ path: 'test-results/artifacts/01-login-mobile-view.png', fullPage: true });
    
    // Check that elements are still visible and properly arranged
    await expect(page.locator('h1')).toBeVisible();
    await expect(page.locator('input[name="username"]')).toBeVisible();
    await expect(page.locator('input[name="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
    
    console.log('✅ Mobile responsiveness verified');
  });

  test('should have proper accessibility features', async ({ page }) => {
    // Check for proper labels and ARIA attributes
    const usernameInput = page.locator('input[name="username"]');
    const passwordInput = page.locator('input[name="password"]');
    
    // Check that inputs have proper labels
    await expect(usernameInput).toHaveAttribute('id', 'username');
    await expect(passwordInput).toHaveAttribute('id', 'password');
    
    // Check label associations
    await expect(page.locator('label[for="username"]')).toBeVisible();
    await expect(page.locator('label[for="password"]')).toBeVisible();
    
    // Take screenshot for accessibility review
    await page.screenshot({ path: 'test-results/artifacts/01-login-accessibility.png', fullPage: true });
    
    console.log('✅ Accessibility features verified');
  });
});
