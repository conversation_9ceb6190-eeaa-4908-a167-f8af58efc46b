const { test, expect } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

test.describe('Application Structure Tests', () => {
  test('should have complete backend structure', async () => {
    const backendPath = path.join(__dirname, '..', 'backend');
    
    // Check main files exist
    const requiredFiles = [
      'app.py',
      'wsgi.py', 
      'requirements.txt',
      'gunicorn.conf.py'
    ];
    
    for (const file of requiredFiles) {
      const filePath = path.join(backendPath, file);
      expect(fs.existsSync(filePath)).toBeTruthy();
      console.log(`✅ Backend file exists: ${file}`);
    }
    
    // Check app directory structure
    const appPath = path.join(backendPath, 'app');
    expect(fs.existsSync(appPath)).toBeTruthy();
    
    const appFiles = [
      '__init__.py',
      'models',
      'routes'
    ];
    
    for (const item of appFiles) {
      const itemPath = path.join(appPath, item);
      expect(fs.existsSync(itemPath)).toBeTruthy();
      console.log(`✅ Backend app structure: ${item}`);
    }
    
    console.log('✅ Complete backend structure verified');
  });

  test('should have complete frontend structure', async () => {
    const frontendPath = path.join(__dirname, '..', 'frontend');
    
    // Check main files exist
    const requiredFiles = [
      'package.json',
      'vite.config.js',
      'tailwind.config.js',
      'index.html'
    ];
    
    for (const file of requiredFiles) {
      const filePath = path.join(frontendPath, file);
      expect(fs.existsSync(filePath)).toBeTruthy();
      console.log(`✅ Frontend file exists: ${file}`);
    }
    
    // Check src directory structure
    const srcPath = path.join(frontendPath, 'src');
    expect(fs.existsSync(srcPath)).toBeTruthy();
    
    const srcFiles = [
      'main.jsx',
      'App.jsx',
      'components',
      'pages',
      'services',
      'styles'
    ];
    
    for (const item of srcFiles) {
      const itemPath = path.join(srcPath, item);
      expect(fs.existsSync(itemPath)).toBeTruthy();
      console.log(`✅ Frontend src structure: ${item}`);
    }
    
    console.log('✅ Complete frontend structure verified');
  });

  test('should have all configuration files', async () => {
    const rootPath = path.join(__dirname, '..');
    
    const configFiles = [
      '.env.example',
      'docker-compose.yml',
      'personal-organizer.service',
      'README.md'
    ];
    
    for (const file of configFiles) {
      const filePath = path.join(rootPath, file);
      expect(fs.existsSync(filePath)).toBeTruthy();
      console.log(`✅ Configuration file exists: ${file}`);
    }
    
    console.log('✅ All configuration files verified');
  });

  test('should have complete model files', async () => {
    const modelsPath = path.join(__dirname, '..', 'backend', 'app', 'models');
    
    const modelFiles = [
      '__init__.py',
      'user.py',
      'bill.py',
      'contact.py',
      'event.py',
      'todo.py',
      'weather.py'
    ];
    
    for (const file of modelFiles) {
      const filePath = path.join(modelsPath, file);
      expect(fs.existsSync(filePath)).toBeTruthy();
      console.log(`✅ Model file exists: ${file}`);
    }
    
    console.log('✅ All model files verified');
  });

  test('should have complete route files', async () => {
    const routesPath = path.join(__dirname, '..', 'backend', 'app', 'routes');
    
    const routeFiles = [
      '__init__.py',
      'auth.py',
      'dashboard.py',
      'bills.py',
      'contacts.py',
      'events.py',
      'todos.py',
      'weather.py',
      'profile.py'
    ];
    
    for (const file of routeFiles) {
      const filePath = path.join(routesPath, file);
      expect(fs.existsSync(filePath)).toBeTruthy();
      console.log(`✅ Route file exists: ${file}`);
    }
    
    console.log('✅ All route files verified');
  });

  test('should have complete React components', async () => {
    const componentsPath = path.join(__dirname, '..', 'frontend', 'src', 'components');
    
    // Check widgets directory
    const widgetsPath = path.join(componentsPath, 'widgets');
    expect(fs.existsSync(widgetsPath)).toBeTruthy();
    
    const widgetFiles = [
      'WeatherWidget.jsx',
      'BillsWidget.jsx',
      'ContactsWidget.jsx',
      'TodosWidget.jsx',
      'EventsWidget.jsx'
    ];
    
    for (const file of widgetFiles) {
      const filePath = path.join(widgetsPath, file);
      expect(fs.existsSync(filePath)).toBeTruthy();
      console.log(`✅ Widget component exists: ${file}`);
    }
    
    // Check forms directory
    const formsPath = path.join(componentsPath, 'forms');
    expect(fs.existsSync(formsPath)).toBeTruthy();
    
    const formFiles = [
      'BillForm.jsx',
      'ContactForm.jsx',
      'EventForm.jsx',
      'TodoForm.jsx'
    ];
    
    for (const file of formFiles) {
      const filePath = path.join(formsPath, file);
      expect(fs.existsSync(filePath)).toBeTruthy();
      console.log(`✅ Form component exists: ${file}`);
    }
    
    console.log('✅ All React components verified');
  });

  test('should have memory bank documentation', async () => {
    const memoryBankPath = path.join(__dirname, '..', 'memory-bank');
    expect(fs.existsSync(memoryBankPath)).toBeTruthy();
    
    const memoryFiles = [
      'projectbrief.md',
      'productContext.md',
      'activeContext.md',
      'systemPatterns.md',
      'techContext.md',
      'progress.md'
    ];
    
    for (const file of memoryFiles) {
      const filePath = path.join(memoryBankPath, file);
      expect(fs.existsSync(filePath)).toBeTruthy();
      console.log(`✅ Memory bank file exists: ${file}`);
    }
    
    console.log('✅ Complete memory bank documentation verified');
  });

  test('should have test infrastructure', async () => {
    const testsPath = path.join(__dirname);
    
    const testFiles = [
      '01-login-page.spec.js',
      '02-dashboard.spec.js',
      '03-api-endpoints.spec.js',
      '04-responsive-design.spec.js'
    ];
    
    for (const file of testFiles) {
      const filePath = path.join(testsPath, file);
      expect(fs.existsSync(filePath)).toBeTruthy();
      console.log(`✅ Test file exists: ${file}`);
    }
    
    // Check playwright config
    const playwrightConfig = path.join(__dirname, '..', 'playwright.config.js');
    expect(fs.existsSync(playwrightConfig)).toBeTruthy();
    console.log('✅ Playwright configuration exists');
    
    console.log('✅ Complete test infrastructure verified');
  });
});
