// Global setup for Playwright tests
const { chromium } = require('@playwright/test');

async function globalSetup(config) {
  console.log('🚀 Starting Personal Organizer Test Suite');
  console.log('📋 Test Configuration:');
  console.log('  - Browser: Firefox (Exclusive)');
  console.log('  - Frontend: http://localhost:5173');
  console.log('  - Backend: http://localhost:5000');
  console.log('  - Screenshots: Enabled for all tests');
  console.log('  - Video: Enabled for all tests');
  
  // Wait for services to be ready
  console.log('⏳ Waiting for services to start...');
  
  // Give services time to start
  await new Promise(resolve => setTimeout(resolve, 10000));
  
  console.log('✅ Global setup complete');
}

module.exports = globalSetup;
