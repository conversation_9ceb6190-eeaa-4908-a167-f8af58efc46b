"""
Database initialization script for Personal Organizer
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, mongo
from app.models.contact import ContactType
from app.models.user import User
from datetime import datetime

def init_database():
    """Initialize database with indexes and default data"""
    app = create_app()
    
    with app.app_context():
        try:
            print("Initializing Personal Organizer database...")
            
            # Create TTL indexes for automatic cleanup
            print("Creating TTL indexes...")
            mongo.db.weather_cache.create_index("fetched_at", expireAfterSeconds=7200)  # 2 hours
            mongo.db.todos.create_index("completed_at", expireAfterSeconds=86400, sparse=True)  # 24 hours
            
            # Create compound indexes for performance
            print("Creating performance indexes...")
            mongo.db.bills.create_index([("user_id", 1), ("due_date", 1)])
            mongo.db.events.create_index([("user_id", 1), ("start", 1)])
            mongo.db.contacts.create_index([("user_id", 1), ("last_contact", 1)])
            mongo.db.todos.create_index([("user_id", 1), ("created_at", -1)])
            
            # Create unique indexes
            print("Creating unique indexes...")
            mongo.db.users.create_index("username", unique=True)
            
            # Create default contact types if none exist
            print("Creating default contact types...")
            ContactType.create_defaults()
            
            print("Database initialization completed successfully!")
            
            # Check if any users exist
            user_count = mongo.db.users.count_documents({})
            if user_count == 0:
                print("\nNo users found. You can create the first user by:")
                print("1. Starting the application")
                print("2. Making a POST request to /api/auth/register")
                print("3. Or using the create_admin_user.py script")
            else:
                print(f"\nFound {user_count} existing user(s) in the database.")
            
        except Exception as e:
            print(f"Error initializing database: {e}")
            return False
    
    return True

if __name__ == "__main__":
    success = init_database()
    sys.exit(0 if success else 1)
