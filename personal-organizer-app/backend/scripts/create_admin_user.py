"""
<PERSON><PERSON><PERSON> to create the initial admin user for Personal Organizer
"""
import sys
import os
import getpass
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from app.models.user import User
from app.models.contact import ContactType

def create_admin_user():
    """Create the initial admin user"""
    app = create_app()
    
    with app.app_context():
        try:
            # Check if any users already exist
            from app import mongo
            if mongo.db.users.count_documents({}) > 0:
                print("Error: Users already exist in the database.")
                print("This is a single-user system. Only one user is allowed.")
                return False
            
            print("Creating initial admin user for Personal Organizer...")
            print("This is a single-user system, so only one user can be created.")
            
            # Get user input
            username = input("Enter username (min 3 characters): ").strip()
            if len(username) < 3:
                print("Error: Username must be at least 3 characters long.")
                return False
            
            password = getpass.getpass("Enter password (min 6 characters): ")
            if len(password) < 6:
                print("Error: Password must be at least 6 characters long.")
                return False
            
            password_confirm = getpass.getpass("Confirm password: ")
            if password != password_confirm:
                print("Error: Passwords do not match.")
                return False
            
            email = input("Enter email (optional): ").strip() or None
            phone = input("Enter phone (optional): ").strip() or None
            
            # Create user
            user = User.create_user(
                username=username,
                password=password,
                email=email,
                phone=phone
            )
            
            # Create default contact types
            ContactType.create_defaults()
            
            print(f"\nAdmin user '{username}' created successfully!")
            print("You can now log in to the Personal Organizer application.")
            
            return True
            
        except ValueError as e:
            print(f"Error: {e}")
            return False
        except Exception as e:
            print(f"Error creating admin user: {e}")
            return False

if __name__ == "__main__":
    success = create_admin_user()
    sys.exit(0 if success else 1)
