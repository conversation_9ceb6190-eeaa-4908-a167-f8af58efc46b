"""
Personal Organizer Flask Application
Main application factory and configuration
"""
from flask import Flask
from flask_login import <PERSON><PERSON><PERSON>ana<PERSON>
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_pymongo import PyMongo
from decouple import config
import logging
from datetime import datetime
import os

# Initialize extensions
mongo = PyMongo()
login_manager = LoginManager()
limiter = Limiter(
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

def create_app():
    """Application factory pattern"""
    app = Flask(__name__)
    
    # Configuration
    app.config['SECRET_KEY'] = config('SECRET_KEY', default='dev-secret-key-change-in-production')
    app.config['MONGO_URI'] = config('MONGODB_URI', default='mongodb://localhost:27017/personal_organizer')
    app.config['TIMEZONE'] = config('TIMEZONE', default='America/Chicago')
    app.config['WEATHER_API_BASE'] = config('WEATHER_API_BASE', default='https://api.open-meteo.com/v1')
    
    # CORS configuration
    @app.after_request
    def after_request(response):
        origin = config('FRONTEND_URL', default='http://localhost:5173')
        response.headers.add('Access-Control-Allow-Origin', origin)
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
        response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,PATCH,OPTIONS')
        response.headers.add('Access-Control-Allow-Credentials', 'true')
        return response
    
    # Initialize extensions with app
    mongo.init_app(app)
    login_manager.init_app(app)
    limiter.init_app(app)
    
    # Login manager configuration
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Please log in to access this page.'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        from app.models.user import User
        return User.get_by_id(user_id)
    
    # Register blueprints
    from app.routes.auth import auth_bp
    from app.routes.dashboard import dashboard_bp
    from app.routes.bills import bills_bp
    from app.routes.contacts import contacts_bp
    from app.routes.events import events_bp
    from app.routes.todos import todos_bp
    from app.routes.weather import weather_bp
    from app.routes.profile import profile_bp
    
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(dashboard_bp, url_prefix='/api')
    app.register_blueprint(bills_bp, url_prefix='/api')
    app.register_blueprint(contacts_bp, url_prefix='/api')
    app.register_blueprint(events_bp, url_prefix='/api')
    app.register_blueprint(todos_bp, url_prefix='/api')
    app.register_blueprint(weather_bp, url_prefix='/api')
    app.register_blueprint(profile_bp, url_prefix='/api')
    
    # Error handlers
    @app.errorhandler(404)
    def not_found(error):
        return {'error': 'Resource not found'}, 404
    
    @app.errorhandler(500)
    def internal_error(error):
        app.logger.error(f'Server Error: {error}')
        return {'error': 'Internal server error'}, 500
    
    @app.errorhandler(429)
    def ratelimit_handler(e):
        return {'error': 'Rate limit exceeded', 'message': str(e.description)}, 429
    
    # Logging configuration
    if not app.debug:
        if not os.path.exists('logs'):
            os.mkdir('logs')
        file_handler = logging.FileHandler('logs/personal_organizer.log')
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        app.logger.setLevel(logging.INFO)
        app.logger.info('Personal Organizer startup')
    
    # Initialize database indexes
    with app.app_context():
        init_database()
    
    return app

def init_database():
    """Initialize database collections and indexes"""
    try:
        # Create TTL indexes for automatic cleanup
        mongo.db.weather_cache.create_index("fetched_at", expireAfterSeconds=7200)  # 2 hours
        mongo.db.todos.create_index("completed_at", expireAfterSeconds=86400, sparse=True)  # 24 hours
        
        # Create compound indexes for performance
        mongo.db.bills.create_index([("user_id", 1), ("due_date", 1)])
        mongo.db.events.create_index([("user_id", 1), ("start", 1)])
        mongo.db.contacts.create_index([("user_id", 1), ("last_contact", 1)])
        mongo.db.todos.create_index([("user_id", 1), ("created_at", -1)])
        
        print("Database indexes initialized successfully")
    except Exception as e:
        print(f"Error initializing database: {e}")
