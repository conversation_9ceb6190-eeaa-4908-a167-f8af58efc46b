"""
Contacts routes for managing contacts and contact types with CRUD operations
"""
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from app.models.contact import Contact, ContactType
from datetime import datetime
from dateutil.parser import parse as parse_date

contacts_bp = Blueprint('contacts', __name__)

@contacts_bp.route('/contacts', methods=['GET'])
@login_required
def get_contacts():
    """Get all contacts for the current user"""
    try:
        user_id = current_user.get_id()
        contacts = Contact.get_user_contacts(user_id)
        
        return jsonify({
            'contacts': [contact.to_dict() for contact in contacts],
            'count': len(contacts)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get contacts error: {e}")
        return jsonify({'error': 'Failed to fetch contacts'}), 500

@contacts_bp.route('/contacts', methods=['POST'])
@login_required
def create_contact():
    """Create a new contact"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        name = data.get('name', '').strip()
        if not name:
            return jsonify({'error': 'Name is required'}), 400
        
        type_id = data.get('type_id')
        if type_id and not ContactType.get_by_id(type_id):
            return jsonify({'error': 'Invalid contact type'}), 400
        
        # Parse last contact date if provided
        last_contact = None
        if data.get('last_contact'):
            try:
                last_contact = parse_date(data['last_contact'])
            except ValueError:
                return jsonify({'error': 'Invalid last contact date format'}), 400
        
        notes = data.get('notes', '').strip() or None
        
        # Create contact
        contact = Contact(
            user_id=current_user.get_id(),
            name=name,
            type_id=type_id,
            last_contact=last_contact,
            notes=notes
        )
        
        contact.save()
        
        current_app.logger.info(f"Contact created: {name} by user {current_user.username}")
        
        return jsonify({
            'message': 'Contact created successfully',
            'contact': contact.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Create contact error: {e}")
        return jsonify({'error': 'Failed to create contact'}), 500

@contacts_bp.route('/contacts/<contact_id>', methods=['GET'])
@login_required
def get_contact(contact_id):
    """Get a specific contact"""
    try:
        contact = Contact.get_by_id(contact_id, current_user.get_id())
        
        if not contact:
            return jsonify({'error': 'Contact not found'}), 404
        
        return jsonify({'contact': contact.to_dict()}), 200
        
    except Exception as e:
        current_app.logger.error(f"Get contact error: {e}")
        return jsonify({'error': 'Failed to fetch contact'}), 500

@contacts_bp.route('/contacts/<contact_id>', methods=['PATCH'])
@login_required
def update_contact(contact_id):
    """Update a specific contact"""
    try:
        contact = Contact.get_by_id(contact_id, current_user.get_id())
        
        if not contact:
            return jsonify({'error': 'Contact not found'}), 404
        
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        updates = {}
        
        # Update name
        if 'name' in data:
            name = data['name'].strip()
            if not name:
                return jsonify({'error': 'Name cannot be empty'}), 400
            updates['name'] = name
        
        # Update type
        if 'type_id' in data:
            type_id = data['type_id']
            if type_id and not ContactType.get_by_id(type_id):
                return jsonify({'error': 'Invalid contact type'}), 400
            updates['type_id'] = type_id
        
        # Update last contact date
        if 'last_contact' in data:
            if data['last_contact']:
                try:
                    updates['last_contact'] = parse_date(data['last_contact'])
                except ValueError:
                    return jsonify({'error': 'Invalid last contact date format'}), 400
            else:
                updates['last_contact'] = datetime.utcnow()
        
        # Update notes
        if 'notes' in data:
            updates['notes'] = data['notes'].strip() if data['notes'] else None
        
        # Apply updates
        contact.update(**updates)
        
        current_app.logger.info(f"Contact updated: {contact_id} by user {current_user.username}")
        
        return jsonify({
            'message': 'Contact updated successfully',
            'contact': contact.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Update contact error: {e}")
        return jsonify({'error': 'Failed to update contact'}), 500

@contacts_bp.route('/contacts/<contact_id>', methods=['DELETE'])
@login_required
def delete_contact(contact_id):
    """Delete a specific contact"""
    try:
        contact = Contact.get_by_id(contact_id, current_user.get_id())
        
        if not contact:
            return jsonify({'error': 'Contact not found'}), 404
        
        contact_name = contact.name
        contact.delete()
        
        current_app.logger.info(f"Contact deleted: {contact_name} by user {current_user.username}")
        
        return jsonify({'message': 'Contact deleted successfully'}), 200
        
    except Exception as e:
        current_app.logger.error(f"Delete contact error: {e}")
        return jsonify({'error': 'Failed to delete contact'}), 500

@contacts_bp.route('/contacts/<contact_id>/ping', methods=['POST'])
@login_required
def ping_contact(contact_id):
    """Update contact's last contact date to now"""
    try:
        contact = Contact.get_by_id(contact_id, current_user.get_id())
        
        if not contact:
            return jsonify({'error': 'Contact not found'}), 404
        
        contact.ping()
        
        current_app.logger.info(f"Contact pinged: {contact.name} by user {current_user.username}")
        
        return jsonify({
            'message': 'Contact pinged successfully',
            'contact': contact.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Ping contact error: {e}")
        return jsonify({'error': 'Failed to ping contact'}), 500

@contacts_bp.route('/contacts/status', methods=['GET'])
@login_required
def get_contacts_by_status():
    """Get contacts grouped by status color"""
    try:
        user_id = current_user.get_id()
        status_groups = Contact.get_contacts_by_status(user_id)
        
        return jsonify({
            'status_groups': status_groups,
            'summary': {
                'green': len(status_groups['green']),
                'yellow': len(status_groups['yellow']),
                'red': len(status_groups['red']),
                'gray': len(status_groups['gray'])
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get contacts by status error: {e}")
        return jsonify({'error': 'Failed to fetch contact status'}), 500

# Contact Types routes
@contacts_bp.route('/contact-types', methods=['GET'])
@login_required
def get_contact_types():
    """Get all contact types"""
    try:
        contact_types = ContactType.get_all()
        
        return jsonify({
            'contact_types': [ct.to_dict() for ct in contact_types],
            'count': len(contact_types)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get contact types error: {e}")
        return jsonify({'error': 'Failed to fetch contact types'}), 500

@contacts_bp.route('/contact-types', methods=['POST'])
@login_required
def create_contact_type():
    """Create a new contact type"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        label = data.get('label', '').strip()
        if not label:
            return jsonify({'error': 'Label is required'}), 400
        
        try:
            threshold_days = int(data.get('threshold_days', 7))
            if threshold_days < 1:
                return jsonify({'error': 'Threshold days must be positive'}), 400
        except (ValueError, TypeError):
            return jsonify({'error': 'Invalid threshold days'}), 400
        
        # Create contact type
        contact_type = ContactType(label=label, threshold_days=threshold_days)
        contact_type.save()
        
        current_app.logger.info(f"Contact type created: {label} by user {current_user.username}")
        
        return jsonify({
            'message': 'Contact type created successfully',
            'contact_type': contact_type.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Create contact type error: {e}")
        return jsonify({'error': 'Failed to create contact type'}), 500

@contacts_bp.route('/contact-types/<type_id>', methods=['DELETE'])
@login_required
def delete_contact_type(type_id):
    """Delete a contact type"""
    try:
        contact_type = ContactType.get_by_id(type_id)
        
        if not contact_type:
            return jsonify({'error': 'Contact type not found'}), 404
        
        type_label = contact_type.label
        contact_type.delete()
        
        current_app.logger.info(f"Contact type deleted: {type_label} by user {current_user.username}")
        
        return jsonify({'message': 'Contact type deleted successfully'}), 200
        
    except Exception as e:
        current_app.logger.error(f"Delete contact type error: {e}")
        return jsonify({'error': 'Failed to delete contact type'}), 500
