"""
Weather routes for fetching weather data with caching
"""
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required
from app.models.weather import Weather

weather_bp = Blueprint('weather', __name__)

@weather_bp.route('/weather', methods=['GET'])
@login_required
def get_weather():
    """Get weather data for a specific city"""
    try:
        city = request.args.get('city', '').lower().strip()
        
        if not city:
            return jsonify({'error': 'City parameter is required'}), 400
        
        # Validate city
        valid_cities = ['stl', 'accra']
        if city not in valid_cities:
            return jsonify({
                'error': f'Invalid city. Supported cities: {", ".join(valid_cities)}'
            }), 400
        
        # Get weather data (from cache or API)
        weather = Weather.get_weather(city)
        
        return jsonify({
            'weather': weather.to_dict(),
            'city': city
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Weather fetch error for city {city}: {e}")
        return jsonify({
            'error': 'Failed to fetch weather data',
            'city': city
        }), 500

@weather_bp.route('/weather/refresh', methods=['POST'])
@login_required
def refresh_weather():
    """Force refresh weather data for a specific city"""
    try:
        data = request.get_json()
        
        if not data or 'city' not in data:
            return jsonify({'error': 'City is required'}), 400
        
        city = data['city'].lower().strip()
        
        # Validate city
        valid_cities = ['stl', 'accra']
        if city not in valid_cities:
            return jsonify({
                'error': f'Invalid city. Supported cities: {", ".join(valid_cities)}'
            }), 400
        
        # Force fetch from API (bypass cache)
        weather = Weather.fetch_weather_data(city)
        
        current_app.logger.info(f"Weather data refreshed for city: {city}")
        
        return jsonify({
            'message': 'Weather data refreshed successfully',
            'weather': weather.to_dict(),
            'city': city
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Weather refresh error for city {city}: {e}")
        return jsonify({
            'error': 'Failed to refresh weather data',
            'city': city
        }), 500

@weather_bp.route('/weather/all', methods=['GET'])
@login_required
def get_all_weather():
    """Get weather data for all supported cities"""
    try:
        cities = ['stl', 'accra']
        weather_data = {}
        errors = {}
        
        for city in cities:
            try:
                weather = Weather.get_weather(city)
                weather_data[city] = weather.to_dict()
            except Exception as e:
                current_app.logger.error(f"Weather fetch error for city {city}: {e}")
                errors[city] = str(e)
        
        response = {
            'weather': weather_data,
            'cities': cities
        }
        
        if errors:
            response['errors'] = errors
        
        return jsonify(response), 200
        
    except Exception as e:
        current_app.logger.error(f"Get all weather error: {e}")
        return jsonify({'error': 'Failed to fetch weather data'}), 500

@weather_bp.route('/weather/cache-status', methods=['GET'])
@login_required
def get_cache_status():
    """Get weather cache status for all cities"""
    try:
        from app import mongo
        from datetime import datetime, timedelta
        
        cities = ['stl', 'accra']
        cache_status = {}
        
        for city in cities:
            cached_data = mongo.db.weather_cache.find_one({'_id': city})
            
            if cached_data:
                fetched_at = cached_data['fetched_at']
                age = datetime.utcnow() - fetched_at
                is_expired = age >= timedelta(hours=2)
                
                cache_status[city] = {
                    'cached': True,
                    'fetched_at': fetched_at.isoformat(),
                    'age_minutes': int(age.total_seconds() / 60),
                    'is_expired': is_expired,
                    'expires_in_minutes': max(0, 120 - int(age.total_seconds() / 60))
                }
            else:
                cache_status[city] = {
                    'cached': False,
                    'fetched_at': None,
                    'age_minutes': None,
                    'is_expired': True,
                    'expires_in_minutes': 0
                }
        
        return jsonify({
            'cache_status': cache_status,
            'cache_duration_hours': 2
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Cache status error: {e}")
        return jsonify({'error': 'Failed to fetch cache status'}), 500
