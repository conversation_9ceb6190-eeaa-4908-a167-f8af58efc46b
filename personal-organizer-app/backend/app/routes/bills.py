"""
Bills routes for managing bills and vacations with CRUD operations
"""
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from app.models.bill import Bill
from datetime import datetime
from dateutil.parser import parse as parse_date

bills_bp = Blueprint('bills', __name__)

@bills_bp.route('/bills', methods=['GET'])
@login_required
def get_bills():
    """Get all bills for the current user"""
    try:
        user_id = current_user.get_id()
        include_instances = request.args.get('include_instances', 'true').lower() == 'true'
        
        bills = Bill.get_user_bills(user_id, include_instances=include_instances)
        
        return jsonify({
            'bills': [bill.to_dict() for bill in bills],
            'count': len(bills)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get bills error: {e}")
        return jsonify({'error': 'Failed to fetch bills'}), 500

@bills_bp.route('/bills', methods=['POST'])
@login_required
def create_bill():
    """Create a new bill or vacation"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        title = data.get('title', '').strip()
        if not title:
            return jsonify({'error': 'Title is required'}), 400
        
        # Parse due date if provided
        due_date = None
        if data.get('due_date'):
            try:
                due_date = parse_date(data['due_date'])
            except ValueError:
                return jsonify({'error': 'Invalid due date format'}), 400
        
        # Validate RRULE if provided
        rrule_str = data.get('rrule', '').strip() or None
        if rrule_str:
            try:
                from dateutil.rrule import rrulestr
                rrulestr(rrule_str)  # Validate RRULE syntax
            except ValueError:
                return jsonify({'error': 'Invalid RRULE format'}), 400
        
        # Parse amount if provided
        amount = None
        if data.get('amount'):
            try:
                amount = float(data['amount'])
            except (ValueError, TypeError):
                return jsonify({'error': 'Invalid amount format'}), 400
        
        category = data.get('category', '').strip() or None
        notes = data.get('notes', '').strip() or None
        
        # Create bill
        bill = Bill(
            user_id=current_user.get_id(),
            title=title,
            due_date=due_date,
            rrule_str=rrule_str,
            amount=amount,
            category=category,
            notes=notes
        )
        
        bill.save()
        
        # Add category to user's categories if new
        if category and category not in current_user.categories:
            current_user.add_category(category)
        
        current_app.logger.info(f"Bill created: {title} by user {current_user.username}")
        
        return jsonify({
            'message': 'Bill created successfully',
            'bill': bill.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Create bill error: {e}")
        return jsonify({'error': 'Failed to create bill'}), 500

@bills_bp.route('/bills/<bill_id>', methods=['GET'])
@login_required
def get_bill(bill_id):
    """Get a specific bill"""
    try:
        bill = Bill.get_by_id(bill_id, current_user.get_id())
        
        if not bill:
            return jsonify({'error': 'Bill not found'}), 404
        
        return jsonify({'bill': bill.to_dict()}), 200
        
    except Exception as e:
        current_app.logger.error(f"Get bill error: {e}")
        return jsonify({'error': 'Failed to fetch bill'}), 500

@bills_bp.route('/bills/<bill_id>', methods=['PATCH'])
@login_required
def update_bill(bill_id):
    """Update a specific bill"""
    try:
        bill = Bill.get_by_id(bill_id, current_user.get_id())
        
        if not bill:
            return jsonify({'error': 'Bill not found'}), 404
        
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        updates = {}
        
        # Update title
        if 'title' in data:
            title = data['title'].strip()
            if not title:
                return jsonify({'error': 'Title cannot be empty'}), 400
            updates['title'] = title
        
        # Update due date
        if 'due_date' in data:
            if data['due_date']:
                try:
                    updates['due_date'] = parse_date(data['due_date'])
                except ValueError:
                    return jsonify({'error': 'Invalid due date format'}), 400
            else:
                updates['due_date'] = None
        
        # Update RRULE
        if 'rrule' in data:
            rrule_str = data['rrule'].strip() if data['rrule'] else None
            if rrule_str:
                try:
                    from dateutil.rrule import rrulestr
                    rrulestr(rrule_str)  # Validate RRULE syntax
                except ValueError:
                    return jsonify({'error': 'Invalid RRULE format'}), 400
            updates['rrule'] = rrule_str
        
        # Update amount
        if 'amount' in data:
            if data['amount'] is not None:
                try:
                    updates['amount'] = float(data['amount'])
                except (ValueError, TypeError):
                    return jsonify({'error': 'Invalid amount format'}), 400
            else:
                updates['amount'] = None
        
        # Update category
        if 'category' in data:
            category = data['category'].strip() if data['category'] else None
            updates['category'] = category
            
            # Add to user categories if new
            if category and category not in current_user.categories:
                current_user.add_category(category)
        
        # Update notes
        if 'notes' in data:
            updates['notes'] = data['notes'].strip() if data['notes'] else None
        
        # Apply updates
        bill.update(**updates)
        
        current_app.logger.info(f"Bill updated: {bill_id} by user {current_user.username}")
        
        return jsonify({
            'message': 'Bill updated successfully',
            'bill': bill.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Update bill error: {e}")
        return jsonify({'error': 'Failed to update bill'}), 500

@bills_bp.route('/bills/<bill_id>', methods=['DELETE'])
@login_required
def delete_bill(bill_id):
    """Delete a specific bill"""
    try:
        bill = Bill.get_by_id(bill_id, current_user.get_id())
        
        if not bill:
            return jsonify({'error': 'Bill not found'}), 404
        
        bill_title = bill.title
        bill.delete()
        
        current_app.logger.info(f"Bill deleted: {bill_title} by user {current_user.username}")
        
        return jsonify({'message': 'Bill deleted successfully'}), 200
        
    except Exception as e:
        current_app.logger.error(f"Delete bill error: {e}")
        return jsonify({'error': 'Failed to delete bill'}), 500

@bills_bp.route('/bills/upcoming', methods=['GET'])
@login_required
def get_upcoming_bills():
    """Get upcoming bills within specified days"""
    try:
        days_ahead = request.args.get('days', 30, type=int)
        if days_ahead < 1 or days_ahead > 365:
            days_ahead = 30
        
        user_id = current_user.get_id()
        upcoming_bills = Bill.get_upcoming_bills(user_id, days_ahead=days_ahead)
        
        return jsonify({
            'bills': upcoming_bills,
            'count': len(upcoming_bills),
            'days_ahead': days_ahead
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get upcoming bills error: {e}")
        return jsonify({'error': 'Failed to fetch upcoming bills'}), 500

@bills_bp.route('/bills/categories', methods=['GET'])
@login_required
def get_bill_categories():
    """Get all bill categories for the user"""
    try:
        return jsonify({
            'categories': current_user.categories
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get categories error: {e}")
        return jsonify({'error': 'Failed to fetch categories'}), 500

@bills_bp.route('/bills/categories', methods=['POST'])
@login_required
def add_bill_category():
    """Add a new bill category"""
    try:
        data = request.get_json()
        
        if not data or not data.get('category'):
            return jsonify({'error': 'Category name is required'}), 400
        
        category = data['category'].strip()
        
        if not category:
            return jsonify({'error': 'Category name cannot be empty'}), 400
        
        if category in current_user.categories:
            return jsonify({'error': 'Category already exists'}), 400
        
        current_user.add_category(category)
        
        return jsonify({
            'message': 'Category added successfully',
            'categories': current_user.categories
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Add category error: {e}")
        return jsonify({'error': 'Failed to add category'}), 500

@bills_bp.route('/bills/categories/<category>', methods=['DELETE'])
@login_required
def remove_bill_category(category):
    """Remove a bill category"""
    try:
        if category not in current_user.categories:
            return jsonify({'error': 'Category not found'}), 404

        current_user.remove_category(category)

        return jsonify({
            'message': 'Category removed successfully',
            'categories': current_user.categories
        }), 200

    except Exception as e:
        current_app.logger.error(f"Remove category error: {e}")
        return jsonify({'error': 'Failed to remove category'}), 500
