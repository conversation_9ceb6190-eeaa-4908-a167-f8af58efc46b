"""
Profile routes for managing user profile and settings
"""
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user

profile_bp = Blueprint('profile', __name__)

@profile_bp.route('/profile', methods=['GET'])
@login_required
def get_profile():
    """Get current user profile"""
    try:
        return jsonify({
            'profile': current_user.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get profile error: {e}")
        return jsonify({'error': 'Failed to fetch profile'}), 500

@profile_bp.route('/profile', methods=['PATCH'])
@login_required
def update_profile():
    """Update user profile"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Validate and prepare updates
        updates = {}
        
        # Update username
        if 'username' in data:
            username = data['username'].strip()
            if not username:
                return jsonify({'error': 'Username cannot be empty'}), 400
            if len(username) < 3:
                return jsonify({'error': 'Username must be at least 3 characters'}), 400
            updates['username'] = username
        
        # Update email
        if 'email' in data:
            email = data['email'].strip() if data['email'] else None
            if email:
                # Basic email validation
                if '@' not in email or '.' not in email.split('@')[1]:
                    return jsonify({'error': 'Invalid email format'}), 400
            updates['email'] = email
        
        # Update phone
        if 'phone' in data:
            phone = data['phone'].strip() if data['phone'] else None
            updates['phone'] = phone
        
        # Update password
        if 'password' in data and 'current_password' in data:
            current_password = data['current_password']
            new_password = data['password']
            
            # Verify current password
            if not current_user.check_password(current_password):
                return jsonify({'error': 'Current password is incorrect'}), 400
            
            if len(new_password) < 6:
                return jsonify({'error': 'New password must be at least 6 characters'}), 400
            
            updates['password'] = new_password
        elif 'password' in data:
            return jsonify({'error': 'Current password is required to change password'}), 400
        
        # Apply updates
        if updates:
            current_user.update_profile(**updates)
            current_app.logger.info(f"Profile updated for user: {current_user.username}")
        
        return jsonify({
            'message': 'Profile updated successfully',
            'profile': current_user.to_dict()
        }), 200
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        current_app.logger.error(f"Update profile error: {e}")
        return jsonify({'error': 'Failed to update profile'}), 500

@profile_bp.route('/profile/categories', methods=['GET'])
@login_required
def get_categories():
    """Get user's bill categories"""
    try:
        return jsonify({
            'categories': current_user.categories
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get categories error: {e}")
        return jsonify({'error': 'Failed to fetch categories'}), 500

@profile_bp.route('/profile/categories', methods=['POST'])
@login_required
def add_category():
    """Add a new category"""
    try:
        data = request.get_json()
        
        if not data or 'category' not in data:
            return jsonify({'error': 'Category name is required'}), 400
        
        category = data['category'].strip()
        
        if not category:
            return jsonify({'error': 'Category name cannot be empty'}), 400
        
        if category in current_user.categories:
            return jsonify({'error': 'Category already exists'}), 400
        
        current_user.add_category(category)
        
        return jsonify({
            'message': 'Category added successfully',
            'categories': current_user.categories
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Add category error: {e}")
        return jsonify({'error': 'Failed to add category'}), 500

@profile_bp.route('/profile/categories/<category>', methods=['DELETE'])
@login_required
def remove_category(category):
    """Remove a category"""
    try:
        if category not in current_user.categories:
            return jsonify({'error': 'Category not found'}), 404
        
        current_user.remove_category(category)
        
        return jsonify({
            'message': 'Category removed successfully',
            'categories': current_user.categories
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Remove category error: {e}")
        return jsonify({'error': 'Failed to remove category'}), 500

@profile_bp.route('/profile/dashboard-layout', methods=['GET'])
@login_required
def get_dashboard_layout():
    """Get user's dashboard layout"""
    try:
        return jsonify({
            'layout': current_user.dashboard_layout
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get dashboard layout error: {e}")
        return jsonify({'error': 'Failed to fetch dashboard layout'}), 500

@profile_bp.route('/profile/dashboard-layout', methods=['POST'])
@login_required
def update_dashboard_layout():
    """Update user's dashboard layout"""
    try:
        data = request.get_json()
        
        if not data or 'layout' not in data:
            return jsonify({'error': 'Layout data is required'}), 400
        
        layout = data['layout']
        
        # Validate layout structure
        if not isinstance(layout, dict):
            return jsonify({'error': 'Layout must be an object'}), 400
        
        current_user.update_dashboard_layout(layout)
        
        return jsonify({
            'message': 'Dashboard layout updated successfully',
            'layout': layout
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Update dashboard layout error: {e}")
        return jsonify({'error': 'Failed to update dashboard layout'}), 500
