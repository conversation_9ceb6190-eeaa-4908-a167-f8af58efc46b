"""
Events routes for managing appointments and activities with CRUD operations
"""
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from app.models.event import Event
from datetime import datetime
from dateutil.parser import parse as parse_date

events_bp = Blueprint('events', __name__)

@events_bp.route('/events', methods=['GET'])
@login_required
def get_events():
    """Get all events for the current user"""
    try:
        user_id = current_user.get_id()
        include_instances = request.args.get('include_instances', 'true').lower() == 'true'
        
        events = Event.get_user_events(user_id, include_instances=include_instances)
        
        return jsonify({
            'events': [event.to_dict() for event in events],
            'count': len(events)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get events error: {e}")
        return jsonify({'error': 'Failed to fetch events'}), 500

@events_bp.route('/events', methods=['POST'])
@login_required
def create_event():
    """Create a new event"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        title = data.get('title', '').strip()
        if not title:
            return jsonify({'error': 'Title is required'}), 400
        
        # Parse start time
        if not data.get('start'):
            return jsonify({'error': 'Start time is required'}), 400
        
        try:
            start = parse_date(data['start'])
        except ValueError:
            return jsonify({'error': 'Invalid start time format'}), 400
        
        # Parse end time if provided
        end = None
        if data.get('end'):
            try:
                end = parse_date(data['end'])
                if end <= start:
                    return jsonify({'error': 'End time must be after start time'}), 400
            except ValueError:
                return jsonify({'error': 'Invalid end time format'}), 400
        
        # Validate RRULE if provided
        rrule_str = data.get('rrule', '').strip() or None
        if rrule_str:
            try:
                from dateutil.rrule import rrulestr
                rrulestr(rrule_str)  # Validate RRULE syntax
            except ValueError:
                return jsonify({'error': 'Invalid RRULE format'}), 400
        
        notes = data.get('notes', '').strip() or None
        
        # Create event
        event = Event(
            user_id=current_user.get_id(),
            title=title,
            start=start,
            end=end,
            rrule_str=rrule_str,
            notes=notes
        )
        
        event.save()
        
        current_app.logger.info(f"Event created: {title} by user {current_user.username}")
        
        return jsonify({
            'message': 'Event created successfully',
            'event': event.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Create event error: {e}")
        return jsonify({'error': 'Failed to create event'}), 500

@events_bp.route('/events/<event_id>', methods=['GET'])
@login_required
def get_event(event_id):
    """Get a specific event"""
    try:
        event = Event.get_by_id(event_id, current_user.get_id())
        
        if not event:
            return jsonify({'error': 'Event not found'}), 404
        
        return jsonify({'event': event.to_dict()}), 200
        
    except Exception as e:
        current_app.logger.error(f"Get event error: {e}")
        return jsonify({'error': 'Failed to fetch event'}), 500

@events_bp.route('/events/<event_id>', methods=['PATCH'])
@login_required
def update_event(event_id):
    """Update a specific event"""
    try:
        event = Event.get_by_id(event_id, current_user.get_id())
        
        if not event:
            return jsonify({'error': 'Event not found'}), 404
        
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        updates = {}
        
        # Update title
        if 'title' in data:
            title = data['title'].strip()
            if not title:
                return jsonify({'error': 'Title cannot be empty'}), 400
            updates['title'] = title
        
        # Update start time
        if 'start' in data:
            try:
                updates['start'] = parse_date(data['start'])
            except ValueError:
                return jsonify({'error': 'Invalid start time format'}), 400
        
        # Update end time
        if 'end' in data:
            if data['end']:
                try:
                    end_time = parse_date(data['end'])
                    start_time = updates.get('start', event.start)
                    if end_time <= start_time:
                        return jsonify({'error': 'End time must be after start time'}), 400
                    updates['end'] = end_time
                except ValueError:
                    return jsonify({'error': 'Invalid end time format'}), 400
            else:
                updates['end'] = None
        
        # Update RRULE
        if 'rrule' in data:
            rrule_str = data['rrule'].strip() if data['rrule'] else None
            if rrule_str:
                try:
                    from dateutil.rrule import rrulestr
                    rrulestr(rrule_str)  # Validate RRULE syntax
                except ValueError:
                    return jsonify({'error': 'Invalid RRULE format'}), 400
            updates['rrule'] = rrule_str
        
        # Update notes
        if 'notes' in data:
            updates['notes'] = data['notes'].strip() if data['notes'] else None
        
        # Apply updates
        event.update(**updates)
        
        current_app.logger.info(f"Event updated: {event_id} by user {current_user.username}")
        
        return jsonify({
            'message': 'Event updated successfully',
            'event': event.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Update event error: {e}")
        return jsonify({'error': 'Failed to update event'}), 500

@events_bp.route('/events/<event_id>', methods=['DELETE'])
@login_required
def delete_event(event_id):
    """Delete a specific event"""
    try:
        event = Event.get_by_id(event_id, current_user.get_id())
        
        if not event:
            return jsonify({'error': 'Event not found'}), 404
        
        event_title = event.title
        event.delete()
        
        current_app.logger.info(f"Event deleted: {event_title} by user {current_user.username}")
        
        return jsonify({'message': 'Event deleted successfully'}), 200
        
    except Exception as e:
        current_app.logger.error(f"Delete event error: {e}")
        return jsonify({'error': 'Failed to delete event'}), 500

@events_bp.route('/events/upcoming', methods=['GET'])
@login_required
def get_upcoming_events():
    """Get upcoming events within specified days"""
    try:
        days_ahead = request.args.get('days', 7, type=int)
        if days_ahead < 1 or days_ahead > 365:
            days_ahead = 7
        
        user_id = current_user.get_id()
        upcoming_events = Event.get_upcoming_events(user_id, days_ahead=days_ahead)
        
        return jsonify({
            'events': upcoming_events,
            'count': len(upcoming_events),
            'days_ahead': days_ahead
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get upcoming events error: {e}")
        return jsonify({'error': 'Failed to fetch upcoming events'}), 500
