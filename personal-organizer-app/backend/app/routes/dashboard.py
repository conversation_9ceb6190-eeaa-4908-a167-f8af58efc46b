"""
Dashboard routes for aggregated data display
"""
from flask import Blueprint, jsonify, current_app
from flask_login import login_required, current_user
from app.models.bill import Bill
from app.models.event import Event
from app.models.contact import Contact
from app.models.todo import Todo
from app.models.weather import Weather
from datetime import datetime, timedelta

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/dashboard', methods=['GET'])
@login_required
def get_dashboard_data():
    """Get aggregated dashboard data for all widgets"""
    try:
        user_id = current_user.get_id()
        dashboard_data = {}
        
        # Get weather data for both cities
        try:
            stl_weather = Weather.get_weather('stl')
            dashboard_data['weather_stl'] = stl_weather.to_dict()
        except Exception as e:
            current_app.logger.error(f"Error fetching St. Louis weather: {e}")
            dashboard_data['weather_stl'] = None
        
        try:
            accra_weather = Weather.get_weather('accra')
            dashboard_data['weather_accra'] = accra_weather.to_dict()
        except Exception as e:
            current_app.logger.error(f"Error fetching Accra weather: {e}")
            dashboard_data['weather_accra'] = None
        
        # Get upcoming bills and vacations (next 30 days)
        try:
            upcoming_bills = Bill.get_upcoming_bills(user_id, days_ahead=30)
            dashboard_data['upcoming_bills'] = upcoming_bills
        except Exception as e:
            current_app.logger.error(f"Error fetching upcoming bills: {e}")
            dashboard_data['upcoming_bills'] = []
        
        # Get upcoming events (next 7 days)
        try:
            upcoming_events = Event.get_upcoming_events(user_id, days_ahead=7)
            dashboard_data['upcoming_events'] = upcoming_events
        except Exception as e:
            current_app.logger.error(f"Error fetching upcoming events: {e}")
            dashboard_data['upcoming_events'] = []
        
        # Get contact status summary
        try:
            contact_status = Contact.get_contacts_by_status(user_id)
            dashboard_data['contact_status'] = contact_status
        except Exception as e:
            current_app.logger.error(f"Error fetching contact status: {e}")
            dashboard_data['contact_status'] = {'green': [], 'yellow': [], 'red': [], 'gray': []}
        
        # Get recent todos (limit to 50 for performance)
        try:
            recent_todos = Todo.get_user_todos(user_id, include_completed=False, limit=50)
            todo_count = Todo.get_user_todo_count(user_id, include_completed=False)
            dashboard_data['todos'] = {
                'items': [todo.to_dict() for todo in recent_todos],
                'total_count': todo_count,
                'showing_count': len(recent_todos)
            }
        except Exception as e:
            current_app.logger.error(f"Error fetching todos: {e}")
            dashboard_data['todos'] = {'items': [], 'total_count': 0, 'showing_count': 0}
        
        # Get bills by category for pie chart
        try:
            bills_by_category = get_bills_by_category(user_id)
            dashboard_data['bills_by_category'] = bills_by_category
        except Exception as e:
            current_app.logger.error(f"Error fetching bills by category: {e}")
            dashboard_data['bills_by_category'] = []
        
        # Add dashboard layout
        dashboard_data['layout'] = current_user.dashboard_layout
        
        # Add user categories
        dashboard_data['categories'] = current_user.categories
        
        # Add timestamp
        dashboard_data['fetched_at'] = datetime.utcnow().isoformat()
        
        return jsonify(dashboard_data), 200
        
    except Exception as e:
        current_app.logger.error(f"Dashboard data error: {e}")
        return jsonify({'error': 'Failed to fetch dashboard data'}), 500

@dashboard_bp.route('/layout', methods=['POST'])
@login_required
def save_dashboard_layout():
    """Save dashboard layout configuration"""
    try:
        from flask import request
        data = request.get_json()
        
        if not data or 'layout' not in data:
            return jsonify({'error': 'Layout data is required'}), 400
        
        layout = data['layout']
        
        # Validate layout structure
        if not isinstance(layout, dict):
            return jsonify({'error': 'Layout must be an object'}), 400
        
        # Update user's dashboard layout
        current_user.update_dashboard_layout(layout)
        
        current_app.logger.info(f"Dashboard layout updated for user: {current_user.username}")
        
        return jsonify({
            'message': 'Layout saved successfully',
            'layout': layout
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Layout save error: {e}")
        return jsonify({'error': 'Failed to save layout'}), 500

def get_bills_by_category(user_id):
    """Get bills grouped by category for pie chart"""
    try:
        from app import mongo
        from bson import ObjectId
        
        # Aggregate bills by category
        pipeline = [
            {'$match': {'user_id': ObjectId(user_id)}},
            {'$group': {
                '_id': '$category',
                'count': {'$sum': 1},
                'total_amount': {'$sum': {'$ifNull': ['$amount', 0]}}
            }},
            {'$sort': {'total_amount': -1}}
        ]
        
        result = list(mongo.db.bills.aggregate(pipeline))
        
        # Format for frontend
        categories = []
        for item in result:
            category = item['_id'] or 'Uncategorized'
            categories.append({
                'category': category,
                'count': item['count'],
                'total_amount': item['total_amount']
            })
        
        return categories
        
    except Exception as e:
        current_app.logger.error(f"Error aggregating bills by category: {e}")
        return []

@dashboard_bp.route('/summary', methods=['GET'])
@login_required
def get_dashboard_summary():
    """Get dashboard summary statistics"""
    try:
        user_id = current_user.get_id()
        
        # Count totals
        from app import mongo
        from bson import ObjectId
        
        total_bills = mongo.db.bills.count_documents({'user_id': ObjectId(user_id)})
        total_events = mongo.db.events.count_documents({'user_id': ObjectId(user_id)})
        total_contacts = mongo.db.contacts.count_documents({'user_id': ObjectId(user_id)})
        total_todos = mongo.db.todos.count_documents({
            'user_id': ObjectId(user_id),
            'completed': {'$ne': True}
        })
        
        # Get overdue bills count
        overdue_bills = mongo.db.bills.count_documents({
            'user_id': ObjectId(user_id),
            'due_date': {'$lt': datetime.utcnow()},
            'rrule': {'$exists': False}
        })
        
        overdue_instances = mongo.db.bill_instances.count_documents({
            'user_id': ObjectId(user_id),
            'due_date': {'$lt': datetime.utcnow()}
        })
        
        # Get contacts needing attention (red status)
        contacts_needing_attention = len(Contact.get_contacts_by_status(user_id)['red'])
        
        summary = {
            'total_bills': total_bills,
            'total_events': total_events,
            'total_contacts': total_contacts,
            'total_todos': total_todos,
            'overdue_bills': overdue_bills + overdue_instances,
            'contacts_needing_attention': contacts_needing_attention,
            'generated_at': datetime.utcnow().isoformat()
        }
        
        return jsonify(summary), 200
        
    except Exception as e:
        current_app.logger.error(f"Dashboard summary error: {e}")
        return jsonify({'error': 'Failed to fetch dashboard summary'}), 500
