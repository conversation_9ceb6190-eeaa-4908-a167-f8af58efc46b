"""
Todos routes for managing unlimited to-do items with CRUD operations
"""
from flask import Blueprint, request, jsonify, current_app
from flask_login import login_required, current_user
from app.models.todo import Todo

todos_bp = Blueprint('todos', __name__)

@todos_bp.route('/todos', methods=['GET'])
@login_required
def get_todos():
    """Get todos for the current user with pagination"""
    try:
        user_id = current_user.get_id()
        
        # Pagination parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        include_completed = request.args.get('include_completed', 'false').lower() == 'true'
        
        # Validate pagination
        if page < 1:
            page = 1
        if per_page < 1 or per_page > 100:
            per_page = 50
        
        skip = (page - 1) * per_page
        
        # Get todos
        todos = Todo.get_user_todos(
            user_id=user_id,
            include_completed=include_completed,
            limit=per_page,
            skip=skip
        )
        
        # Get total count
        total_count = Todo.get_user_todo_count(user_id, include_completed=include_completed)
        
        return jsonify({
            'todos': [todo.to_dict() for todo in todos],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_count,
                'pages': (total_count + per_page - 1) // per_page,
                'has_next': skip + per_page < total_count,
                'has_prev': page > 1
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get todos error: {e}")
        return jsonify({'error': 'Failed to fetch todos'}), 500

@todos_bp.route('/todos', methods=['POST'])
@login_required
def create_todo():
    """Create a new todo"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        text = data.get('text', '').strip()
        if not text:
            return jsonify({'error': 'Todo text is required'}), 400
        
        if len(text) > 500:
            return jsonify({'error': 'Todo text too long (max 500 characters)'}), 400
        
        # Create todo
        todo = Todo.create_todo(user_id=current_user.get_id(), text=text)
        
        current_app.logger.info(f"Todo created by user {current_user.username}")
        
        return jsonify({
            'message': 'Todo created successfully',
            'todo': todo.to_dict()
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Create todo error: {e}")
        return jsonify({'error': 'Failed to create todo'}), 500

@todos_bp.route('/todos/<todo_id>', methods=['GET'])
@login_required
def get_todo(todo_id):
    """Get a specific todo"""
    try:
        todo = Todo.get_by_id(todo_id, current_user.get_id())
        
        if not todo:
            return jsonify({'error': 'Todo not found'}), 404
        
        return jsonify({'todo': todo.to_dict()}), 200
        
    except Exception as e:
        current_app.logger.error(f"Get todo error: {e}")
        return jsonify({'error': 'Failed to fetch todo'}), 500

@todos_bp.route('/todos/<todo_id>', methods=['PATCH'])
@login_required
def update_todo(todo_id):
    """Update a specific todo"""
    try:
        todo = Todo.get_by_id(todo_id, current_user.get_id())
        
        if not todo:
            return jsonify({'error': 'Todo not found'}), 404
        
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        updates = {}
        
        # Update text
        if 'text' in data:
            text = data['text'].strip()
            if not text:
                return jsonify({'error': 'Todo text cannot be empty'}), 400
            if len(text) > 500:
                return jsonify({'error': 'Todo text too long (max 500 characters)'}), 400
            updates['text'] = text
        
        # Update completed status
        if 'completed' in data:
            updates['completed'] = bool(data['completed'])
        
        # Apply updates
        todo.update(**updates)
        
        current_app.logger.info(f"Todo updated: {todo_id} by user {current_user.username}")
        
        return jsonify({
            'message': 'Todo updated successfully',
            'todo': todo.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Update todo error: {e}")
        return jsonify({'error': 'Failed to update todo'}), 500

@todos_bp.route('/todos/<todo_id>', methods=['DELETE'])
@login_required
def delete_todo(todo_id):
    """Delete a specific todo"""
    try:
        todo = Todo.get_by_id(todo_id, current_user.get_id())
        
        if not todo:
            return jsonify({'error': 'Todo not found'}), 404
        
        todo.delete()
        
        current_app.logger.info(f"Todo deleted: {todo_id} by user {current_user.username}")
        
        return jsonify({'message': 'Todo deleted successfully'}), 200
        
    except Exception as e:
        current_app.logger.error(f"Delete todo error: {e}")
        return jsonify({'error': 'Failed to delete todo'}), 500

@todos_bp.route('/todos/<todo_id>/complete', methods=['POST'])
@login_required
def complete_todo(todo_id):
    """Mark a todo as completed"""
    try:
        todo = Todo.get_by_id(todo_id, current_user.get_id())
        
        if not todo:
            return jsonify({'error': 'Todo not found'}), 404
        
        if todo.completed:
            return jsonify({'error': 'Todo already completed'}), 400
        
        todo.complete()
        
        current_app.logger.info(f"Todo completed: {todo_id} by user {current_user.username}")
        
        return jsonify({
            'message': 'Todo completed successfully',
            'todo': todo.to_dict()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Complete todo error: {e}")
        return jsonify({'error': 'Failed to complete todo'}), 500

@todos_bp.route('/todos/bulk-delete-completed', methods=['DELETE'])
@login_required
def bulk_delete_completed():
    """Delete all completed todos for the user"""
    try:
        user_id = current_user.get_id()
        deleted_count = Todo.bulk_delete_completed(user_id)
        
        current_app.logger.info(f"Bulk deleted {deleted_count} completed todos for user {current_user.username}")
        
        return jsonify({
            'message': f'Deleted {deleted_count} completed todos',
            'deleted_count': deleted_count
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Bulk delete completed todos error: {e}")
        return jsonify({'error': 'Failed to delete completed todos'}), 500

@todos_bp.route('/todos/recent-completed', methods=['GET'])
@login_required
def get_recent_completed():
    """Get recently completed todos"""
    try:
        user_id = current_user.get_id()
        limit = request.args.get('limit', 10, type=int)
        
        if limit < 1 or limit > 50:
            limit = 10
        
        recent_todos = Todo.get_recent_completed(user_id, limit=limit)
        
        return jsonify({
            'todos': [todo.to_dict() for todo in recent_todos],
            'count': len(recent_todos)
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get recent completed todos error: {e}")
        return jsonify({'error': 'Failed to fetch recent completed todos'}), 500

@todos_bp.route('/todos/stats', methods=['GET'])
@login_required
def get_todo_stats():
    """Get todo statistics for the user"""
    try:
        user_id = current_user.get_id()
        
        total_active = Todo.get_user_todo_count(user_id, include_completed=False)
        total_completed = Todo.get_user_todo_count(user_id, include_completed=True) - total_active
        
        return jsonify({
            'active_count': total_active,
            'completed_count': total_completed,
            'total_count': total_active + total_completed
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Get todo stats error: {e}")
        return jsonify({'error': 'Failed to fetch todo statistics'}), 500
