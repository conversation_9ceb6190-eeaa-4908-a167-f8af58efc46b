"""
Event model for managing appointments and activities with recurrence support
"""
from bson import ObjectId
from datetime import datetime, timedelta
from dateutil.rrule import rrulestr
from app import mongo

class Event:
    def __init__(self, user_id, title, start, end=None, rrule_str=None, notes=None):
        self.user_id = ObjectId(user_id) if isinstance(user_id, str) else user_id
        self.title = title
        self.start = start
        self.end = end or (start + timedelta(hours=1))  # Default 1 hour duration
        self.rrule_str = rrule_str
        self.notes = notes
        self.created_at = datetime.utcnow()
        self._id = None
    
    def save(self):
        """Save event to database"""
        event_data = {
            'user_id': self.user_id,
            'title': self.title,
            'start': self.start,
            'end': self.end,
            'rrule': self.rrule_str,
            'notes': self.notes,
            'created_at': self.created_at
        }
        
        if self._id:
            mongo.db.events.update_one(
                {'_id': ObjectId(self._id)},
                {'$set': event_data}
            )
        else:
            result = mongo.db.events.insert_one(event_data)
            self._id = result.inserted_id
        
        # Generate recurring instances if RRULE is present
        if self.rrule_str:
            self._generate_instances()
        
        return self
    
    def update(self, **kwargs):
        """Update event fields"""
        updates = {}
        
        for field in ['title', 'start', 'end', 'rrule', 'notes']:
            if field in kwargs:
                value = kwargs[field]
                setattr(self, field + ('_str' if field == 'rrule' else ''), value)
                updates[field] = value
        
        if updates:
            mongo.db.events.update_one(
                {'_id': ObjectId(self._id)},
                {'$set': updates}
            )
            
            # Regenerate instances if recurrence changed
            if 'rrule' in updates:
                self._delete_instances()
                if self.rrule_str:
                    self._generate_instances()
        
        return self
    
    def delete(self):
        """Delete event and all its instances"""
        if self._id:
            mongo.db.events.delete_one({'_id': ObjectId(self._id)})
            self._delete_instances()
        return True
    
    def _generate_instances(self):
        """Generate recurring instances for the next 12 months"""
        if not self.rrule_str or not self.start:
            return
        
        try:
            rule = rrulestr(self.rrule_str, dtstart=self.start)
            end_date = datetime.utcnow() + timedelta(days=365)
            instances = []
            
            duration = self.end - self.start if self.end else timedelta(hours=1)
            
            for occurrence in rule:
                if occurrence > end_date:
                    break
                
                # Skip past occurrences (older than 1 day)
                if occurrence < datetime.utcnow() - timedelta(days=1):
                    continue
                
                instance_data = {
                    'event_id': self._id,
                    'user_id': self.user_id,
                    'title': self.title,
                    'start': occurrence,
                    'end': occurrence + duration,
                    'notes': self.notes,
                    'is_instance': True,
                    'created_at': datetime.utcnow()
                }
                instances.append(instance_data)
            
            if instances:
                self._delete_instances()
                mongo.db.event_instances.insert_many(instances)
        
        except Exception as e:
            print(f"Error generating event instances: {e}")
    
    def _delete_instances(self):
        """Delete all instances of this event"""
        if self._id:
            mongo.db.event_instances.delete_many({'event_id': ObjectId(self._id)})
    
    @staticmethod
    def get_by_id(event_id, user_id):
        """Get event by ID for specific user"""
        event_data = mongo.db.events.find_one({
            '_id': ObjectId(event_id),
            'user_id': ObjectId(user_id)
        })
        
        if event_data:
            event = Event(
                user_id=event_data['user_id'],
                title=event_data['title'],
                start=event_data['start'],
                end=event_data.get('end'),
                rrule_str=event_data.get('rrule'),
                notes=event_data.get('notes')
            )
            event._id = event_data['_id']
            event.created_at = event_data.get('created_at', datetime.utcnow())
            return event
        return None
    
    @staticmethod
    def get_user_events(user_id, include_instances=True):
        """Get all events for a user"""
        events = []
        
        # Get main events
        event_cursor = mongo.db.events.find({'user_id': ObjectId(user_id)}).sort('start', 1)
        
        for event_data in event_cursor:
            event = Event(
                user_id=event_data['user_id'],
                title=event_data['title'],
                start=event_data['start'],
                end=event_data.get('end'),
                rrule_str=event_data.get('rrule'),
                notes=event_data.get('notes')
            )
            event._id = event_data['_id']
            event.created_at = event_data.get('created_at', datetime.utcnow())
            events.append(event)
        
        # Get instances if requested
        if include_instances:
            instance_cursor = mongo.db.event_instances.find({
                'user_id': ObjectId(user_id),
                'start': {'$gte': datetime.utcnow() - timedelta(days=1)}
            }).sort('start', 1)
            
            for instance_data in instance_cursor:
                instance = Event(
                    user_id=instance_data['user_id'],
                    title=instance_data['title'],
                    start=instance_data['start'],
                    end=instance_data.get('end'),
                    notes=instance_data.get('notes')
                )
                instance._id = instance_data['_id']
                instance.is_instance = True
                instance.event_id = instance_data.get('event_id')
                events.append(instance)
        
        return events
    
    @staticmethod
    def get_upcoming_events(user_id, days_ahead=7):
        """Get upcoming events within specified days"""
        cutoff_date = datetime.utcnow() + timedelta(days=days_ahead)
        
        # Get one-time events
        one_time_events = mongo.db.events.find({
            'user_id': ObjectId(user_id),
            'start': {
                '$gte': datetime.utcnow(),
                '$lte': cutoff_date
            },
            'rrule': {'$exists': False}
        }).sort('start', 1)
        
        # Get recurring event instances
        recurring_instances = mongo.db.event_instances.find({
            'user_id': ObjectId(user_id),
            'start': {
                '$gte': datetime.utcnow(),
                '$lte': cutoff_date
            }
        }).sort('start', 1)
        
        events = []
        
        # Process one-time events
        for event_data in one_time_events:
            events.append({
                'id': str(event_data['_id']),
                'title': event_data['title'],
                'start': event_data['start'].isoformat(),
                'end': event_data['end'].isoformat() if event_data.get('end') else None,
                'notes': event_data.get('notes'),
                'is_instance': False
            })
        
        # Process recurring instances
        for instance_data in recurring_instances:
            events.append({
                'id': str(instance_data['_id']),
                'event_id': str(instance_data.get('event_id')),
                'title': instance_data['title'],
                'start': instance_data['start'].isoformat(),
                'end': instance_data['end'].isoformat() if instance_data.get('end') else None,
                'notes': instance_data.get('notes'),
                'is_instance': True
            })
        
        # Sort by start time
        events.sort(key=lambda x: x['start'])
        return events
    
    def to_dict(self):
        """Convert event to dictionary for JSON serialization"""
        return {
            'id': str(self._id),
            'title': self.title,
            'start': self.start.isoformat() if self.start else None,
            'end': self.end.isoformat() if self.end else None,
            'rrule': self.rrule_str,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'is_instance': getattr(self, 'is_instance', False),
            'event_id': str(getattr(self, 'event_id', '')) if hasattr(self, 'event_id') else None
        }
