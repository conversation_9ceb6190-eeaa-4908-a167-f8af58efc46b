"""
Weather model for caching weather data from Open-Meteo API
"""
from datetime import datetime, timedelta
import requests
from app import mongo
from flask import current_app

class Weather:
    def __init__(self, city, current_temp=None, current_condition=None, forecast=None):
        self.city = city
        self.current_temp = current_temp
        self.current_condition = current_condition
        self.forecast = forecast or []
        self.fetched_at = datetime.utcnow()
    
    def save_to_cache(self):
        """Save weather data to cache with TTL"""
        weather_data = {
            '_id': self.city,
            'current_temp': self.current_temp,
            'current_condition': self.current_condition,
            'forecast': self.forecast,
            'fetched_at': self.fetched_at
        }
        
        mongo.db.weather_cache.replace_one(
            {'_id': self.city},
            weather_data,
            upsert=True
        )
        
        return self
    
    @staticmethod
    def get_from_cache(city):
        """Get weather data from cache if not expired"""
        cached_data = mongo.db.weather_cache.find_one({'_id': city})
        
        if cached_data:
            # Check if cache is still valid (2 hours)
            cache_age = datetime.utcnow() - cached_data['fetched_at']
            if cache_age < timedelta(hours=2):
                weather = Weather(
                    city=city,
                    current_temp=cached_data.get('current_temp'),
                    current_condition=cached_data.get('current_condition'),
                    forecast=cached_data.get('forecast', [])
                )
                weather.fetched_at = cached_data['fetched_at']
                return weather
        
        return None
    
    @staticmethod
    def fetch_weather_data(city):
        """Fetch weather data from Open-Meteo API"""
        try:
            # City coordinates mapping
            city_coords = {
                'stl': {'lat': 38.6270, 'lon': -90.1994, 'name': 'St. Louis'},
                'accra': {'lat': 5.6037, 'lon': -0.1870, 'name': 'Accra'}
            }
            
            if city not in city_coords:
                raise ValueError(f"Unknown city: {city}")
            
            coords = city_coords[city]
            
            # Build API URL
            base_url = current_app.config.get('WEATHER_API_BASE', 'https://api.open-meteo.com/v1')
            url = f"{base_url}/forecast"
            
            params = {
                'latitude': coords['lat'],
                'longitude': coords['lon'],
                'current': 'temperature_2m,weather_code',
                'daily': 'temperature_2m_max,temperature_2m_min,weather_code',
                'temperature_unit': 'fahrenheit',
                'forecast_days': 3,
                'timezone': 'auto'
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            # Parse current weather
            current = data.get('current', {})
            current_temp = current.get('temperature_2m')
            current_code = current.get('weather_code', 0)
            current_condition = Weather._get_weather_condition(current_code)
            
            # Parse forecast
            daily = data.get('daily', {})
            forecast = []
            
            if daily:
                dates = daily.get('time', [])
                max_temps = daily.get('temperature_2m_max', [])
                min_temps = daily.get('temperature_2m_min', [])
                codes = daily.get('weather_code', [])
                
                for i, date in enumerate(dates):
                    if i < len(max_temps) and i < len(min_temps) and i < len(codes):
                        forecast.append({
                            'date': date,
                            'max_temp': max_temps[i],
                            'min_temp': min_temps[i],
                            'condition': Weather._get_weather_condition(codes[i]),
                            'weather_code': codes[i]
                        })
            
            # Create weather object
            weather = Weather(
                city=city,
                current_temp=current_temp,
                current_condition=current_condition,
                forecast=forecast
            )
            
            # Save to cache
            weather.save_to_cache()
            
            return weather
            
        except requests.RequestException as e:
            current_app.logger.error(f"Weather API request failed: {e}")
            raise
        except Exception as e:
            current_app.logger.error(f"Weather data processing failed: {e}")
            raise
    
    @staticmethod
    def get_weather(city):
        """Get weather data (from cache or API)"""
        # Try cache first
        weather = Weather.get_from_cache(city)
        
        if weather:
            return weather
        
        # Fetch from API if cache miss or expired
        return Weather.fetch_weather_data(city)
    
    @staticmethod
    def _get_weather_condition(weather_code):
        """Convert weather code to human-readable condition"""
        # WMO Weather interpretation codes
        conditions = {
            0: "Clear sky",
            1: "Mainly clear",
            2: "Partly cloudy",
            3: "Overcast",
            45: "Fog",
            48: "Depositing rime fog",
            51: "Light drizzle",
            53: "Moderate drizzle",
            55: "Dense drizzle",
            56: "Light freezing drizzle",
            57: "Dense freezing drizzle",
            61: "Slight rain",
            63: "Moderate rain",
            65: "Heavy rain",
            66: "Light freezing rain",
            67: "Heavy freezing rain",
            71: "Slight snow fall",
            73: "Moderate snow fall",
            75: "Heavy snow fall",
            77: "Snow grains",
            80: "Slight rain showers",
            81: "Moderate rain showers",
            82: "Violent rain showers",
            85: "Slight snow showers",
            86: "Heavy snow showers",
            95: "Thunderstorm",
            96: "Thunderstorm with slight hail",
            99: "Thunderstorm with heavy hail"
        }
        
        return conditions.get(weather_code, "Unknown")
    
    def to_dict(self):
        """Convert weather to dictionary for JSON serialization"""
        return {
            'city': self.city,
            'current_temp': self.current_temp,
            'current_condition': self.current_condition,
            'forecast': self.forecast,
            'fetched_at': self.fetched_at.isoformat() if self.fetched_at else None,
            'is_cached': True
        }
