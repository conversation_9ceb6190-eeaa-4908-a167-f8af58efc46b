"""
Todo model for managing unlimited to-do items with automatic cleanup
"""
from bson import ObjectId
from datetime import datetime
from app import mongo

class Todo:
    def __init__(self, user_id, text, completed=False):
        self.user_id = ObjectId(user_id) if isinstance(user_id, str) else user_id
        self.text = text
        self.completed = completed
        self.created_at = datetime.utcnow()
        self.completed_at = None
        self._id = None
    
    def save(self):
        """Save todo to database"""
        todo_data = {
            'user_id': self.user_id,
            'text': self.text,
            'completed': self.completed,
            'created_at': self.created_at,
            'completed_at': self.completed_at
        }
        
        if self._id:
            mongo.db.todos.update_one(
                {'_id': ObjectId(self._id)},
                {'$set': todo_data}
            )
        else:
            result = mongo.db.todos.insert_one(todo_data)
            self._id = result.inserted_id
        
        return self
    
    def update(self, **kwargs):
        """Update todo fields"""
        updates = {}
        
        if 'text' in kwargs:
            self.text = kwargs['text']
            updates['text'] = self.text
        
        if 'completed' in kwargs:
            self.completed = kwargs['completed']
            updates['completed'] = self.completed
            
            if self.completed and not self.completed_at:
                self.completed_at = datetime.utcnow()
                updates['completed_at'] = self.completed_at
            elif not self.completed:
                self.completed_at = None
                updates['completed_at'] = None
        
        if updates:
            mongo.db.todos.update_one(
                {'_id': ObjectId(self._id)},
                {'$set': updates}
            )
        
        return self
    
    def complete(self):
        """Mark todo as completed"""
        self.completed = True
        self.completed_at = datetime.utcnow()
        
        # Update in database with TTL for automatic cleanup
        mongo.db.todos.update_one(
            {'_id': ObjectId(self._id)},
            {'$set': {
                'completed': True,
                'completed_at': self.completed_at
            }}
        )
        
        return self
    
    def delete(self):
        """Delete todo immediately (hard delete)"""
        if self._id:
            mongo.db.todos.delete_one({'_id': ObjectId(self._id)})
        return True
    
    @staticmethod
    def get_by_id(todo_id, user_id):
        """Get todo by ID for specific user"""
        todo_data = mongo.db.todos.find_one({
            '_id': ObjectId(todo_id),
            'user_id': ObjectId(user_id)
        })
        
        if todo_data:
            todo = Todo(
                user_id=todo_data['user_id'],
                text=todo_data['text'],
                completed=todo_data.get('completed', False)
            )
            todo._id = todo_data['_id']
            todo.created_at = todo_data.get('created_at', datetime.utcnow())
            todo.completed_at = todo_data.get('completed_at')
            return todo
        return None
    
    @staticmethod
    def get_user_todos(user_id, include_completed=False, limit=None, skip=0):
        """Get todos for a user with pagination support"""
        query = {'user_id': ObjectId(user_id)}
        
        if not include_completed:
            query['completed'] = {'$ne': True}
        
        cursor = mongo.db.todos.find(query).sort('created_at', -1)
        
        if skip:
            cursor = cursor.skip(skip)
        
        if limit:
            cursor = cursor.limit(limit)
        
        todos = []
        for todo_data in cursor:
            todo = Todo(
                user_id=todo_data['user_id'],
                text=todo_data['text'],
                completed=todo_data.get('completed', False)
            )
            todo._id = todo_data['_id']
            todo.created_at = todo_data.get('created_at', datetime.utcnow())
            todo.completed_at = todo_data.get('completed_at')
            todos.append(todo)
        
        return todos
    
    @staticmethod
    def get_user_todo_count(user_id, include_completed=False):
        """Get total count of todos for a user"""
        query = {'user_id': ObjectId(user_id)}
        
        if not include_completed:
            query['completed'] = {'$ne': True}
        
        return mongo.db.todos.count_documents(query)
    
    @staticmethod
    def create_todo(user_id, text):
        """Create a new todo"""
        todo = Todo(user_id=user_id, text=text)
        return todo.save()
    
    @staticmethod
    def bulk_delete_completed(user_id):
        """Delete all completed todos for a user"""
        result = mongo.db.todos.delete_many({
            'user_id': ObjectId(user_id),
            'completed': True
        })
        return result.deleted_count
    
    @staticmethod
    def get_recent_completed(user_id, limit=10):
        """Get recently completed todos"""
        todos = []
        cursor = mongo.db.todos.find({
            'user_id': ObjectId(user_id),
            'completed': True,
            'completed_at': {'$exists': True}
        }).sort('completed_at', -1).limit(limit)
        
        for todo_data in cursor:
            todo = Todo(
                user_id=todo_data['user_id'],
                text=todo_data['text'],
                completed=True
            )
            todo._id = todo_data['_id']
            todo.created_at = todo_data.get('created_at', datetime.utcnow())
            todo.completed_at = todo_data.get('completed_at')
            todos.append(todo)
        
        return todos
    
    def to_dict(self):
        """Convert todo to dictionary for JSON serialization"""
        return {
            'id': str(self._id),
            'text': self.text,
            'completed': self.completed,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None
        }
