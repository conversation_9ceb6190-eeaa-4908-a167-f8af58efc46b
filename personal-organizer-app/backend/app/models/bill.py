"""
Bill model for managing bills and vacations with recurrence support
"""
from bson import ObjectId
from datetime import datetime, timedelta
from dateutil.rrule import rrule, rrulestr
from dateutil.parser import parse as parse_date
import pytz
from app import mongo

class Bill:
    def __init__(self, user_id, title, due_date=None, rrule_str=None, amount=None, category=None, notes=None):
        self.user_id = ObjectId(user_id) if isinstance(user_id, str) else user_id
        self.title = title
        self.due_date = due_date
        self.rrule_str = rrule_str
        self.amount = amount
        self.category = category
        self.notes = notes
        self.created_at = datetime.utcnow()
        self._id = None
    
    def save(self):
        """Save bill to database"""
        bill_data = {
            'user_id': self.user_id,
            'title': self.title,
            'due_date': self.due_date,
            'rrule': self.rrule_str,
            'amount': self.amount,
            'category': self.category,
            'notes': self.notes,
            'created_at': self.created_at
        }
        
        if self._id:
            # Update existing bill
            mongo.db.bills.update_one(
                {'_id': ObjectId(self._id)},
                {'$set': bill_data}
            )
        else:
            # Create new bill
            result = mongo.db.bills.insert_one(bill_data)
            self._id = result.inserted_id
        
        # Generate recurring instances if RRULE is present
        if self.rrule_str:
            self._generate_instances()
        
        return self
    
    def update(self, **kwargs):
        """Update bill fields"""
        updates = {}
        
        for field in ['title', 'due_date', 'rrule', 'amount', 'category', 'notes']:
            if field in kwargs:
                value = kwargs[field]
                setattr(self, field + ('_str' if field == 'rrule' else ''), value)
                updates[field] = value
        
        if updates:
            mongo.db.bills.update_one(
                {'_id': ObjectId(self._id)},
                {'$set': updates}
            )
            
            # Regenerate instances if recurrence changed
            if 'rrule' in updates:
                self._delete_instances()
                if self.rrule_str:
                    self._generate_instances()
        
        return self
    
    def delete(self):
        """Delete bill and all its instances"""
        if self._id:
            # Delete the main bill
            mongo.db.bills.delete_one({'_id': ObjectId(self._id)})
            # Delete all instances
            self._delete_instances()
        return True
    
    def _generate_instances(self):
        """Generate recurring instances for the next 12 months"""
        if not self.rrule_str or not self.due_date:
            return
        
        try:
            # Parse the RRULE
            rule = rrulestr(self.rrule_str, dtstart=self.due_date)
            
            # Generate instances for the next 12 months
            end_date = datetime.utcnow() + timedelta(days=365)
            instances = []
            
            for occurrence in rule:
                if occurrence > end_date:
                    break
                
                # Skip past occurrences (older than 1 day)
                if occurrence < datetime.utcnow() - timedelta(days=1):
                    continue
                
                instance_data = {
                    'bill_id': self._id,
                    'user_id': self.user_id,
                    'title': self.title,
                    'due_date': occurrence,
                    'amount': self.amount,
                    'category': self.category,
                    'notes': self.notes,
                    'is_instance': True,
                    'created_at': datetime.utcnow()
                }
                instances.append(instance_data)
            
            if instances:
                # Delete existing instances first
                self._delete_instances()
                # Insert new instances
                mongo.db.bill_instances.insert_many(instances)
        
        except Exception as e:
            print(f"Error generating bill instances: {e}")
    
    def _delete_instances(self):
        """Delete all instances of this bill"""
        if self._id:
            mongo.db.bill_instances.delete_many({'bill_id': ObjectId(self._id)})
    
    @staticmethod
    def get_by_id(bill_id, user_id):
        """Get bill by ID for specific user"""
        bill_data = mongo.db.bills.find_one({
            '_id': ObjectId(bill_id),
            'user_id': ObjectId(user_id)
        })
        
        if bill_data:
            bill = Bill(
                user_id=bill_data['user_id'],
                title=bill_data['title'],
                due_date=bill_data.get('due_date'),
                rrule_str=bill_data.get('rrule'),
                amount=bill_data.get('amount'),
                category=bill_data.get('category'),
                notes=bill_data.get('notes')
            )
            bill._id = bill_data['_id']
            bill.created_at = bill_data.get('created_at', datetime.utcnow())
            return bill
        return None
    
    @staticmethod
    def get_user_bills(user_id, include_instances=True):
        """Get all bills for a user"""
        bills = []
        
        # Get main bills
        bill_cursor = mongo.db.bills.find({'user_id': ObjectId(user_id)}).sort('created_at', -1)
        
        for bill_data in bill_cursor:
            bill = Bill(
                user_id=bill_data['user_id'],
                title=bill_data['title'],
                due_date=bill_data.get('due_date'),
                rrule_str=bill_data.get('rrule'),
                amount=bill_data.get('amount'),
                category=bill_data.get('category'),
                notes=bill_data.get('notes')
            )
            bill._id = bill_data['_id']
            bill.created_at = bill_data.get('created_at', datetime.utcnow())
            bills.append(bill)
        
        # Get instances if requested
        if include_instances:
            instance_cursor = mongo.db.bill_instances.find({
                'user_id': ObjectId(user_id),
                'due_date': {'$gte': datetime.utcnow() - timedelta(days=1)}
            }).sort('due_date', 1)
            
            for instance_data in instance_cursor:
                instance = Bill(
                    user_id=instance_data['user_id'],
                    title=instance_data['title'],
                    due_date=instance_data.get('due_date'),
                    amount=instance_data.get('amount'),
                    category=instance_data.get('category'),
                    notes=instance_data.get('notes')
                )
                instance._id = instance_data['_id']
                instance.is_instance = True
                instance.bill_id = instance_data.get('bill_id')
                bills.append(instance)
        
        return bills
    
    @staticmethod
    def get_upcoming_bills(user_id, days_ahead=30):
        """Get upcoming bills within specified days"""
        cutoff_date = datetime.utcnow() + timedelta(days=days_ahead)
        
        # Get one-time bills
        one_time_bills = mongo.db.bills.find({
            'user_id': ObjectId(user_id),
            'due_date': {
                '$gte': datetime.utcnow(),
                '$lte': cutoff_date
            },
            'rrule': {'$exists': False}
        }).sort('due_date', 1)
        
        # Get recurring bill instances
        recurring_instances = mongo.db.bill_instances.find({
            'user_id': ObjectId(user_id),
            'due_date': {
                '$gte': datetime.utcnow(),
                '$lte': cutoff_date
            }
        }).sort('due_date', 1)
        
        bills = []
        
        # Process one-time bills
        for bill_data in one_time_bills:
            bills.append({
                'id': str(bill_data['_id']),
                'title': bill_data['title'],
                'due_date': bill_data['due_date'].isoformat(),
                'amount': bill_data.get('amount'),
                'category': bill_data.get('category'),
                'notes': bill_data.get('notes'),
                'is_instance': False
            })
        
        # Process recurring instances
        for instance_data in recurring_instances:
            bills.append({
                'id': str(instance_data['_id']),
                'bill_id': str(instance_data.get('bill_id')),
                'title': instance_data['title'],
                'due_date': instance_data['due_date'].isoformat(),
                'amount': instance_data.get('amount'),
                'category': instance_data.get('category'),
                'notes': instance_data.get('notes'),
                'is_instance': True
            })
        
        # Sort by due date
        bills.sort(key=lambda x: x['due_date'])
        return bills
    
    def to_dict(self):
        """Convert bill to dictionary for JSON serialization"""
        return {
            'id': str(self._id),
            'title': self.title,
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'rrule': self.rrule_str,
            'amount': self.amount,
            'category': self.category,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'is_instance': getattr(self, 'is_instance', False),
            'bill_id': str(getattr(self, 'bill_id', '')) if hasattr(self, 'bill_id') else None
        }
