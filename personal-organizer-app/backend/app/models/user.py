"""
User model for authentication and profile management
"""
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from bson import ObjectId
from datetime import datetime
from app import mongo

class User(UserMixin):
    def __init__(self, username, email=None, phone=None, dashboard_layout=None, categories=None):
        self.username = username
        self.email = email
        self.phone = phone
        self.dashboard_layout = dashboard_layout or self.get_default_layout()
        self.categories = categories or self.get_default_categories()
        self.created_at = datetime.utcnow()
        self._id = None
    
    def get_id(self):
        """Required by Flask-Login"""
        return str(self._id) if self._id else None
    
    def set_password(self, password):
        """Hash and set password"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check password against hash"""
        return check_password_hash(self.password_hash, password)
    
    def save(self):
        """Save user to database"""
        user_data = {
            'username': self.username,
            'password_hash': self.password_hash,
            'email': self.email,
            'phone': self.phone,
            'dashboard_layout': self.dashboard_layout,
            'categories': self.categories,
            'created_at': self.created_at
        }
        
        if self._id:
            # Update existing user
            mongo.db.users.update_one(
                {'_id': ObjectId(self._id)},
                {'$set': user_data}
            )
        else:
            # Create new user
            result = mongo.db.users.insert_one(user_data)
            self._id = result.inserted_id
        
        return self
    
    def update_profile(self, username=None, email=None, phone=None, password=None):
        """Update user profile"""
        updates = {}
        
        if username and username != self.username:
            # Check if username is already taken
            if mongo.db.users.find_one({'username': username, '_id': {'$ne': ObjectId(self._id)}}):
                raise ValueError('Username already exists')
            updates['username'] = username
            self.username = username
        
        if email is not None:
            updates['email'] = email
            self.email = email
        
        if phone is not None:
            updates['phone'] = phone
            self.phone = phone
        
        if password:
            updates['password_hash'] = generate_password_hash(password)
            self.password_hash = updates['password_hash']
        
        if updates:
            mongo.db.users.update_one(
                {'_id': ObjectId(self._id)},
                {'$set': updates}
            )
        
        return self
    
    def update_dashboard_layout(self, layout):
        """Update dashboard layout"""
        self.dashboard_layout = layout
        mongo.db.users.update_one(
            {'_id': ObjectId(self._id)},
            {'$set': {'dashboard_layout': layout}}
        )
        return self
    
    def add_category(self, category):
        """Add a new category"""
        if category not in self.categories:
            self.categories.append(category)
            mongo.db.users.update_one(
                {'_id': ObjectId(self._id)},
                {'$addToSet': {'categories': category}}
            )
        return self
    
    def remove_category(self, category):
        """Remove a category"""
        if category in self.categories:
            self.categories.remove(category)
            mongo.db.users.update_one(
                {'_id': ObjectId(self._id)},
                {'$pull': {'categories': category}}
            )
        return self
    
    @staticmethod
    def get_by_id(user_id):
        """Get user by ID"""
        try:
            user_data = mongo.db.users.find_one({'_id': ObjectId(user_id)})
            if user_data:
                user = User(
                    username=user_data['username'],
                    email=user_data.get('email'),
                    phone=user_data.get('phone'),
                    dashboard_layout=user_data.get('dashboard_layout'),
                    categories=user_data.get('categories')
                )
                user._id = user_data['_id']
                user.password_hash = user_data['password_hash']
                user.created_at = user_data.get('created_at', datetime.utcnow())
                return user
        except Exception:
            pass
        return None
    
    @staticmethod
    def get_by_username(username):
        """Get user by username"""
        user_data = mongo.db.users.find_one({'username': username})
        if user_data:
            user = User(
                username=user_data['username'],
                email=user_data.get('email'),
                phone=user_data.get('phone'),
                dashboard_layout=user_data.get('dashboard_layout'),
                categories=user_data.get('categories')
            )
            user._id = user_data['_id']
            user.password_hash = user_data['password_hash']
            user.created_at = user_data.get('created_at', datetime.utcnow())
            return user
        return None
    
    @staticmethod
    def create_user(username, password, email=None, phone=None):
        """Create a new user"""
        # Check if username already exists
        if mongo.db.users.find_one({'username': username}):
            raise ValueError('Username already exists')
        
        user = User(username=username, email=email, phone=phone)
        user.set_password(password)
        return user.save()
    
    @staticmethod
    def get_default_layout():
        """Default dashboard layout"""
        return {
            "lg": [
                {"i": "weather-current", "x": 0, "y": 0, "w": 2, "h": 3},
                {"i": "weather-forecast", "x": 2, "y": 0, "w": 2, "h": 3},
                {"i": "bills-upcoming", "x": 0, "y": 3, "w": 4, "h": 4},
                {"i": "contacts-status", "x": 0, "y": 7, "w": 2, "h": 3},
                {"i": "todos", "x": 2, "y": 7, "w": 2, "h": 3},
                {"i": "events-upcoming", "x": 0, "y": 10, "w": 4, "h": 3}
            ]
        }
    
    @staticmethod
    def get_default_categories():
        """Default bill categories"""
        return [
            "Housing", "Utilities", "Insurance", "Transportation", 
            "Healthcare", "Personal", "Entertainment", "Travel"
        ]
    
    def to_dict(self):
        """Convert user to dictionary for JSON serialization"""
        return {
            'id': str(self._id),
            'username': self.username,
            'email': self.email,
            'phone': self.phone,
            'dashboard_layout': self.dashboard_layout,
            'categories': self.categories,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
