"""
Contact model for managing contacts with color-coded thresholds
"""
from bson import ObjectId
from datetime import datetime, timedelta
from app import mongo

class ContactType:
    def __init__(self, label, threshold_days):
        self.label = label
        self.threshold_days = threshold_days
        self._id = None
    
    def save(self):
        """Save contact type to database"""
        type_data = {
            'label': self.label,
            'threshold_days': self.threshold_days
        }
        
        if self._id:
            mongo.db.contact_types.update_one(
                {'_id': ObjectId(self._id)},
                {'$set': type_data}
            )
        else:
            result = mongo.db.contact_types.insert_one(type_data)
            self._id = result.inserted_id
        
        return self
    
    def delete(self):
        """Delete contact type and update associated contacts"""
        if self._id:
            # Update contacts to remove this type
            mongo.db.contacts.update_many(
                {'type_id': ObjectId(self._id)},
                {'$unset': {'type_id': ''}}
            )
            # Delete the contact type
            mongo.db.contact_types.delete_one({'_id': ObjectId(self._id)})
        return True
    
    @staticmethod
    def get_all():
        """Get all contact types"""
        types = []
        for type_data in mongo.db.contact_types.find().sort('threshold_days', 1):
            contact_type = ContactType(
                label=type_data['label'],
                threshold_days=type_data['threshold_days']
            )
            contact_type._id = type_data['_id']
            types.append(contact_type)
        return types
    
    @staticmethod
    def get_by_id(type_id):
        """Get contact type by ID"""
        type_data = mongo.db.contact_types.find_one({'_id': ObjectId(type_id)})
        if type_data:
            contact_type = ContactType(
                label=type_data['label'],
                threshold_days=type_data['threshold_days']
            )
            contact_type._id = type_data['_id']
            return contact_type
        return None
    
    @staticmethod
    def create_defaults():
        """Create default contact types if none exist"""
        if mongo.db.contact_types.count_documents({}) == 0:
            defaults = [
                {'label': 'Close Friends', 'threshold_days': 3},
                {'label': 'Family', 'threshold_days': 7},
                {'label': 'Colleagues', 'threshold_days': 21}
            ]
            mongo.db.contact_types.insert_many(defaults)
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': str(self._id),
            'label': self.label,
            'threshold_days': self.threshold_days
        }

class Contact:
    def __init__(self, user_id, name, type_id=None, last_contact=None, notes=None):
        self.user_id = ObjectId(user_id) if isinstance(user_id, str) else user_id
        self.name = name
        self.type_id = ObjectId(type_id) if type_id and isinstance(type_id, str) else type_id
        self.last_contact = last_contact or datetime.utcnow()
        self.notes = notes
        self.created_at = datetime.utcnow()
        self._id = None
    
    def save(self):
        """Save contact to database"""
        contact_data = {
            'user_id': self.user_id,
            'name': self.name,
            'type_id': self.type_id,
            'last_contact': self.last_contact,
            'notes': self.notes,
            'created_at': self.created_at
        }
        
        if self._id:
            mongo.db.contacts.update_one(
                {'_id': ObjectId(self._id)},
                {'$set': contact_data}
            )
        else:
            result = mongo.db.contacts.insert_one(contact_data)
            self._id = result.inserted_id
        
        return self
    
    def update(self, **kwargs):
        """Update contact fields"""
        updates = {}
        
        for field in ['name', 'type_id', 'last_contact', 'notes']:
            if field in kwargs:
                value = kwargs[field]
                if field == 'type_id' and value:
                    value = ObjectId(value) if isinstance(value, str) else value
                setattr(self, field, value)
                updates[field] = value
        
        if updates:
            mongo.db.contacts.update_one(
                {'_id': ObjectId(self._id)},
                {'$set': updates}
            )
        
        return self
    
    def ping(self):
        """Update last contact to now"""
        self.last_contact = datetime.utcnow()
        mongo.db.contacts.update_one(
            {'_id': ObjectId(self._id)},
            {'$set': {'last_contact': self.last_contact}}
        )
        return self
    
    def delete(self):
        """Delete contact"""
        if self._id:
            mongo.db.contacts.delete_one({'_id': ObjectId(self._id)})
        return True
    
    def get_status_color(self):
        """Get color based on contact threshold"""
        if not self.type_id:
            return 'gray'
        
        contact_type = ContactType.get_by_id(self.type_id)
        if not contact_type:
            return 'gray'
        
        days_since_contact = (datetime.utcnow() - self.last_contact).days
        threshold = contact_type.threshold_days
        
        if days_since_contact <= threshold:
            return 'green'
        elif days_since_contact <= threshold * 2:
            return 'yellow'
        else:
            return 'red'
    
    @staticmethod
    def get_by_id(contact_id, user_id):
        """Get contact by ID for specific user"""
        contact_data = mongo.db.contacts.find_one({
            '_id': ObjectId(contact_id),
            'user_id': ObjectId(user_id)
        })
        
        if contact_data:
            contact = Contact(
                user_id=contact_data['user_id'],
                name=contact_data['name'],
                type_id=contact_data.get('type_id'),
                last_contact=contact_data.get('last_contact'),
                notes=contact_data.get('notes')
            )
            contact._id = contact_data['_id']
            contact.created_at = contact_data.get('created_at', datetime.utcnow())
            return contact
        return None
    
    @staticmethod
    def get_user_contacts(user_id):
        """Get all contacts for a user with status colors"""
        contacts = []
        contact_cursor = mongo.db.contacts.find({'user_id': ObjectId(user_id)}).sort('name', 1)
        
        for contact_data in contact_cursor:
            contact = Contact(
                user_id=contact_data['user_id'],
                name=contact_data['name'],
                type_id=contact_data.get('type_id'),
                last_contact=contact_data.get('last_contact'),
                notes=contact_data.get('notes')
            )
            contact._id = contact_data['_id']
            contact.created_at = contact_data.get('created_at', datetime.utcnow())
            contacts.append(contact)
        
        return contacts
    
    @staticmethod
    def get_contacts_by_status(user_id):
        """Get contacts grouped by status color"""
        contacts = Contact.get_user_contacts(user_id)
        status_groups = {'green': [], 'yellow': [], 'red': [], 'gray': []}
        
        for contact in contacts:
            status = contact.get_status_color()
            status_groups[status].append(contact.to_dict())
        
        return status_groups
    
    def to_dict(self):
        """Convert contact to dictionary for JSON serialization"""
        contact_type = ContactType.get_by_id(self.type_id) if self.type_id else None
        
        return {
            'id': str(self._id),
            'name': self.name,
            'type_id': str(self.type_id) if self.type_id else None,
            'type_label': contact_type.label if contact_type else None,
            'threshold_days': contact_type.threshold_days if contact_type else None,
            'last_contact': self.last_contact.isoformat() if self.last_contact else None,
            'days_since_contact': (datetime.utcnow() - self.last_contact).days if self.last_contact else 0,
            'status_color': self.get_status_color(),
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
