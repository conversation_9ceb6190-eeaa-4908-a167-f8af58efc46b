# Personal Organizer Backend Dependencies
# Production-ready Flask application with MongoDB integration

# Core Flask framework
Flask==3.0.0
Werkzeug==3.0.1

# Authentication and session management
Flask-Login==0.6.3

# MongoDB integration
Flask-PyMongo==2.3.0
pymongo==4.6.0

# Rate limiting for security
Flask-Limiter==3.5.0

# Environment variable management
python-decouple==3.8

# Date and time handling with RRULE support
python-dateutil==2.8.2

# HTTP requests for weather API
requests==2.31.0

# WSGI server for production
gunicorn==21.2.0

# CORS handling
Flask-CORS==4.0.0

# JSON handling and validation
jsonschema==4.20.0

# Timezone handling
pytz==2023.3

# Development and testing dependencies (optional)
# Uncomment for development environment
# pytest==7.4.3
# pytest-flask==1.3.0
# black==23.11.0
# flake8==6.1.0
