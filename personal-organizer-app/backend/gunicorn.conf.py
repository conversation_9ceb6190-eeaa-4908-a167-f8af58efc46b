"""
Gunicorn configuration for Personal Organizer
Production-ready WSGI server configuration
"""
import os
import multiprocessing
from decouple import config

# Server socket
bind = config('GUNICORN_BIND', default='127.0.0.1:5000')
backlog = 2048

# Worker processes
workers = config('GUNICORN_WORKERS', default=multiprocessing.cpu_count() * 2 + 1, cast=int)
worker_class = 'sync'
worker_connections = 1000
timeout = config('GUNICORN_TIMEOUT', default=30, cast=int)
keepalive = 2

# Restart workers after this many requests, to help prevent memory leaks
max_requests = 1000
max_requests_jitter = 100

# Preload application for better performance
preload_app = True

# User and group to run as
user = config('GUNICORN_USER', default='www-data')
group = config('GUNICORN_GROUP', default='www-data')

# Logging
accesslog = config('GUNICORN_ACCESS_LOG', default='/var/log/personal-organizer/access.log')
errorlog = config('GUNICORN_ERROR_LOG', default='/var/log/personal-organizer/error.log')
loglevel = config('GUNICORN_LOG_LEVEL', default='info')
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# Process naming
proc_name = 'personal-organizer'

# Server mechanics
daemon = False
pidfile = '/var/run/personal-organizer/personal-organizer.pid'
tmp_upload_dir = None

# SSL (if using HTTPS directly with Gunicorn)
# keyfile = '/path/to/ssl/private.key'
# certfile = '/path/to/ssl/certificate.crt'

# Security
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# Application
pythonpath = '/opt/personal-organizer'
chdir = '/opt/personal-organizer/backend'

# Environment variables
raw_env = [
    'FLASK_ENV=production',
    'PYTHONUNBUFFERED=1',
]

# Hooks
def on_starting(server):
    """Called just before the master process is initialized."""
    server.log.info("Personal Organizer starting up...")

def on_reload(server):
    """Called to recycle workers during a reload via SIGHUP."""
    server.log.info("Personal Organizer reloading...")

def when_ready(server):
    """Called just after the server is started."""
    server.log.info("Personal Organizer ready to serve requests")

def worker_int(worker):
    """Called just after a worker exited on SIGINT or SIGQUIT."""
    worker.log.info("Worker received INT or QUIT signal")

def pre_fork(server, worker):
    """Called just before a worker is forked."""
    pass

def post_fork(server, worker):
    """Called just after a worker has been forked."""
    server.log.info("Worker spawned (pid: %s)", worker.pid)

def post_worker_init(worker):
    """Called just after a worker has initialized the application."""
    worker.log.info("Worker initialized")

def worker_abort(worker):
    """Called when a worker received the SIGABRT signal."""
    worker.log.info("Worker received SIGABRT signal")

def pre_exec(server):
    """Called just before a new master process is forked."""
    server.log.info("Forked child, re-executing")

def pre_request(worker, req):
    """Called just before a worker processes the request."""
    worker.log.debug("%s %s", req.method, req.path)

def post_request(worker, req, environ, resp):
    """Called after a worker processes the request."""
    pass
