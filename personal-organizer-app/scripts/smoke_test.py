#!/usr/bin/env python3
"""
Smoke test for Personal Organizer application
Tests basic functionality and API endpoints
"""
import sys
import os
import requests
import time
import json
from datetime import datetime

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

def test_backend_health():
    """Test if backend is running and responding"""
    try:
        response = requests.get('http://localhost:5000/api/auth/status', timeout=5)
        if response.status_code == 200:
            print("✅ Backend health check passed")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Backend connection failed: {e}")
        return False

def test_database_connection():
    """Test MongoDB connection"""
    try:
        from app import create_app, mongo
        app = create_app()
        
        with app.app_context():
            # Test database connection
            mongo.db.command('ping')
            print("✅ Database connection successful")
            
            # Test collections exist
            collections = mongo.db.list_collection_names()
            expected_collections = ['users', 'bills', 'contacts', 'events', 'todos', 'weather_cache']
            
            for collection in expected_collections:
                if collection in collections:
                    print(f"✅ Collection '{collection}' exists")
                else:
                    print(f"ℹ️  Collection '{collection}' will be created on first use")
            
            return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_weather_api():
    """Test weather API integration"""
    try:
        response = requests.get('http://localhost:5000/api/weather?city=stl', timeout=10)
        if response.status_code == 200:
            data = response.json()
            if 'weather' in data and data['weather']:
                print("✅ Weather API integration working")
                return True
            else:
                print("❌ Weather API returned empty data")
                return False
        else:
            print(f"❌ Weather API test failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Weather API connection failed: {e}")
        return False

def test_frontend_build():
    """Test if frontend build exists"""
    frontend_dist = os.path.join(os.path.dirname(__file__), '..', 'frontend', 'dist')
    index_html = os.path.join(frontend_dist, 'index.html')
    
    if os.path.exists(index_html):
        print("✅ Frontend build exists")
        return True
    else:
        print("❌ Frontend build not found. Run 'npm run build' in frontend directory")
        return False

def test_environment_config():
    """Test environment configuration"""
    env_file = os.path.join(os.path.dirname(__file__), '..', '.env')
    env_example = os.path.join(os.path.dirname(__file__), '..', '.env.example')
    
    if os.path.exists(env_file):
        print("✅ Environment file (.env) exists")
        
        # Check for required variables
        with open(env_file, 'r') as f:
            env_content = f.read()
            
        required_vars = ['SECRET_KEY', 'MONGODB_URI', 'FLASK_ENV']
        for var in required_vars:
            if var in env_content:
                print(f"✅ Required environment variable '{var}' found")
            else:
                print(f"⚠️  Required environment variable '{var}' missing")
        
        return True
    else:
        print(f"⚠️  Environment file not found. Copy {env_example} to .env")
        return False

def test_file_structure():
    """Test if all required files exist"""
    base_dir = os.path.join(os.path.dirname(__file__), '..')
    
    required_files = [
        'backend/app.py',
        'backend/wsgi.py',
        'backend/requirements.txt',
        'backend/gunicorn.conf.py',
        'frontend/package.json',
        'frontend/vite.config.js',
        'personal-organizer.service',
        'docker-compose.yml',
        'README.md'
    ]
    
    all_exist = True
    for file_path in required_files:
        full_path = os.path.join(base_dir, file_path)
        if os.path.exists(full_path):
            print(f"✅ {file_path} exists")
        else:
            print(f"❌ {file_path} missing")
            all_exist = False
    
    return all_exist

def test_python_imports():
    """Test if all Python dependencies can be imported"""
    try:
        import flask
        import flask_login
        import flask_pymongo
        import flask_limiter
        import pymongo
        import requests
        import dateutil
        from decouple import config
        
        print("✅ All Python dependencies can be imported")
        return True
    except ImportError as e:
        print(f"❌ Python import failed: {e}")
        print("Run: pip install -r backend/requirements.txt")
        return False

def run_smoke_tests():
    """Run all smoke tests"""
    print("🧪 Running Personal Organizer Smoke Tests")
    print("=" * 50)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Environment Config", test_environment_config),
        ("Python Imports", test_python_imports),
        ("Database Connection", test_database_connection),
        ("Backend Health", test_backend_health),
        ("Weather API", test_weather_api),
        ("Frontend Build", test_frontend_build),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 Testing {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All smoke tests passed! Application is ready.")
        return True
    else:
        print("⚠️  Some tests failed. Please address the issues above.")
        return False

if __name__ == "__main__":
    success = run_smoke_tests()
    sys.exit(0 if success else 1)
