version: '3.8'

services:
  # MongoDB database
  mongodb:
    image: mongo:7.0
    container_name: personal-organizer-db
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_ROOT_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_ROOT_PASSWORD:-changeme}
      MONGO_INITDB_DATABASE: personal_organizer
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    ports:
      - "27017:27017"
    networks:
      - personal-organizer-network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Flask backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: personal-organizer-api
    restart: unless-stopped
    environment:
      FLASK_ENV: production
      MONGODB_URI: mongodb://admin:${MONGO_ROOT_PASSWORD:-changeme}@mongodb:27017/personal_organizer?authSource=admin
      SECRET_KEY: ${SECRET_KEY:-your-secret-key-change-in-production}
      FRONTEND_URL: http://localhost:3000
      WEATHER_API_BASE: https://api.open-meteo.com/v1
      TIMEZONE: America/Chicago
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/.env:/app/.env:ro
    ports:
      - "5000:5000"
    networks:
      - personal-organizer-network
    depends_on:
      mongodb:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/auth/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # React frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        VITE_API_URL: http://localhost:5000
    container_name: personal-organizer-web
    restart: unless-stopped
    ports:
      - "3000:80"
    networks:
      - personal-organizer-network
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: personal-organizer-proxy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    networks:
      - personal-organizer-network
    depends_on:
      - frontend
      - backend
    profiles:
      - proxy

volumes:
  mongodb_data:
    driver: local

networks:
  personal-organizer-network:
    driver: bridge
