#!/usr/bin/env node
/**
 * Comprehensive Application Verification Script
 * Verifies all components of the Personal Organizer application
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Personal Organizer Application Verification');
console.log('=' * 60);

let totalTests = 0;
let passedTests = 0;

function test(description, testFn) {
  totalTests++;
  try {
    const result = testFn();
    if (result) {
      console.log(`✅ ${description}`);
      passedTests++;
    } else {
      console.log(`❌ ${description}`);
    }
  } catch (error) {
    console.log(`❌ ${description} - Error: ${error.message}`);
  }
}

// Backend Structure Tests
console.log('\n📁 Backend Structure Tests');
console.log('-'.repeat(40));

test('Backend directory exists', () => fs.existsSync('./backend'));
test('Flask app.py exists', () => fs.existsSync('./backend/app.py'));
test('WSGI entry point exists', () => fs.existsSync('./backend/wsgi.py'));
test('Requirements file exists', () => fs.existsSync('./backend/requirements.txt'));
test('Gunicorn config exists', () => fs.existsSync('./backend/gunicorn.conf.py'));

test('App package exists', () => fs.existsSync('./backend/app/__init__.py'));
test('Models package exists', () => fs.existsSync('./backend/app/models/__init__.py'));
test('Routes package exists', () => fs.existsSync('./backend/app/routes/__init__.py'));

// Model Files
const models = ['user.py', 'bill.py', 'contact.py', 'event.py', 'todo.py', 'weather.py'];
models.forEach(model => {
  test(`Model ${model} exists`, () => fs.existsSync(`./backend/app/models/${model}`));
});

// Route Files
const routes = ['auth.py', 'dashboard.py', 'bills.py', 'contacts.py', 'events.py', 'todos.py', 'weather.py', 'profile.py'];
routes.forEach(route => {
  test(`Route ${route} exists`, () => fs.existsSync(`./backend/app/routes/${route}`));
});

// Frontend Structure Tests
console.log('\n🎨 Frontend Structure Tests');
console.log('-'.repeat(40));

test('Frontend directory exists', () => fs.existsSync('./frontend'));
test('Package.json exists', () => fs.existsSync('./frontend/package.json'));
test('Vite config exists', () => fs.existsSync('./frontend/vite.config.js'));
test('Tailwind config exists', () => fs.existsSync('./frontend/tailwind.config.js'));
test('Index.html exists', () => fs.existsSync('./frontend/index.html'));

test('Src directory exists', () => fs.existsSync('./frontend/src'));
test('Main.jsx exists', () => fs.existsSync('./frontend/src/main.jsx'));
test('App.jsx exists', () => fs.existsSync('./frontend/src/App.jsx'));

// Component Files
const components = ['Layout.jsx'];
components.forEach(component => {
  test(`Component ${component} exists`, () => fs.existsSync(`./frontend/src/components/${component}`));
});

// Widget Files
const widgets = ['WeatherWidget.jsx', 'BillsWidget.jsx', 'ContactsWidget.jsx', 'TodosWidget.jsx', 'EventsWidget.jsx'];
widgets.forEach(widget => {
  test(`Widget ${widget} exists`, () => fs.existsSync(`./frontend/src/components/widgets/${widget}`));
});

// Form Files
const forms = ['BillForm.jsx', 'ContactForm.jsx', 'EventForm.jsx', 'TodoForm.jsx'];
forms.forEach(form => {
  test(`Form ${form} exists`, () => fs.existsSync(`./frontend/src/components/forms/${form}`));
});

// Page Files
const pages = ['LoginPage.jsx', 'Dashboard.jsx', 'ProfilePage.jsx'];
pages.forEach(page => {
  test(`Page ${page} exists`, () => fs.existsSync(`./frontend/src/pages/${page}`));
});

// Configuration Files Tests
console.log('\n⚙️  Configuration Files Tests');
console.log('-'.repeat(40));

test('Environment example exists', () => fs.existsSync('./.env.example'));
test('Docker Compose exists', () => fs.existsSync('./docker-compose.yml'));
test('Systemd service exists', () => fs.existsSync('./personal-organizer.service'));
test('Backend Dockerfile exists', () => fs.existsSync('./backend/Dockerfile'));
test('Frontend Dockerfile exists', () => fs.existsSync('./frontend/Dockerfile'));
test('Frontend Nginx config exists', () => fs.existsSync('./frontend/nginx.conf'));

// Documentation Tests
console.log('\n📚 Documentation Tests');
console.log('-'.repeat(40));

test('README.md exists', () => fs.existsSync('./README.md'));
test('Memory bank exists', () => fs.existsSync('./memory-bank'));
test('Project brief exists', () => fs.existsSync('./memory-bank/projectbrief.md'));
test('Product context exists', () => fs.existsSync('./memory-bank/productContext.md'));
test('System patterns exists', () => fs.existsSync('./memory-bank/systemPatterns.md'));
test('Tech context exists', () => fs.existsSync('./memory-bank/techContext.md'));
test('Progress tracking exists', () => fs.existsSync('./memory-bank/progress.md'));

// Testing Infrastructure Tests
console.log('\n🧪 Testing Infrastructure Tests');
console.log('-'.repeat(40));

test('Tests directory exists', () => fs.existsSync('./tests'));
test('Playwright config exists', () => fs.existsSync('./playwright.config.js'));
test('Smoke test exists', () => fs.existsSync('./scripts/smoke_test.py'));

// Content Quality Tests
console.log('\n📝 Content Quality Tests');
console.log('-'.repeat(40));

test('README has content', () => {
  if (!fs.existsSync('./README.md')) return false;
  const content = fs.readFileSync('./README.md', 'utf8');
  return content.length > 1000; // Should be substantial
});

test('Package.json has dependencies', () => {
  if (!fs.existsSync('./frontend/package.json')) return false;
  const pkg = JSON.parse(fs.readFileSync('./frontend/package.json', 'utf8'));
  return pkg.dependencies && Object.keys(pkg.dependencies).length > 5;
});

test('Requirements.txt has dependencies', () => {
  if (!fs.existsSync('./backend/requirements.txt')) return false;
  const content = fs.readFileSync('./backend/requirements.txt', 'utf8');
  return content.split('\n').filter(line => line.trim() && !line.startsWith('#')).length > 5;
});

// File Size Tests (ensure files aren't empty)
console.log('\n📏 File Size Tests');
console.log('-'.repeat(40));

const criticalFiles = [
  './backend/app.py',
  './frontend/src/App.jsx',
  './frontend/src/pages/Dashboard.jsx',
  './backend/app/models/user.py',
  './backend/app/routes/auth.py'
];

criticalFiles.forEach(file => {
  test(`${path.basename(file)} has content`, () => {
    if (!fs.existsSync(file)) return false;
    const stats = fs.statSync(file);
    return stats.size > 100; // At least 100 bytes
  });
});

// Summary
console.log('\n' + '='.repeat(60));
console.log('📊 Test Results Summary');
console.log('='.repeat(60));

const successRate = Math.round((passedTests / totalTests) * 100);

console.log(`Total Tests: ${totalTests}`);
console.log(`Passed: ${passedTests}`);
console.log(`Failed: ${totalTests - passedTests}`);
console.log(`Success Rate: ${successRate}%`);

if (successRate >= 95) {
  console.log('\n🎉 EXCELLENT! Application structure is complete and ready for deployment.');
} else if (successRate >= 85) {
  console.log('\n✅ GOOD! Application structure is mostly complete with minor issues.');
} else if (successRate >= 70) {
  console.log('\n⚠️  FAIR! Application structure needs some attention.');
} else {
  console.log('\n❌ POOR! Application structure has significant issues.');
}

console.log('\n🚀 Personal Organizer Application Verification Complete!');

process.exit(successRate >= 85 ? 0 : 1);
