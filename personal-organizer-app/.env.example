# Personal Organizer Environment Configuration
# Copy this file to .env and update the values for your environment

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Flask environment (development, production)
FLASK_ENV=production

# Secret key for session management and security
# Generate a secure random key: python -c "import secrets; print(secrets.token_hex(32))"
SECRET_KEY=your-super-secret-key-change-this-in-production

# Application timezone (used for date/time display)
TIMEZONE=America/Chicago

# Debug mode (set to False in production)
DEBUG=False

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# MongoDB connection URI
# Local MongoDB: mongodb://localhost:27017/personal_organizer
# MongoDB with auth: **************************************************************
# MongoDB Atlas: mongodb+srv://username:<EMAIL>/personal_organizer
MONGODB_URI=mongodb://localhost:27017/personal_organizer

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================

# Weather API configuration
# Open-Meteo API base URL (free service, no API key required)
WEATHER_API_BASE=https://api.open-meteo.com/v1

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# Frontend URL for CORS configuration
# Development: http://localhost:5173
# Production: https://your-domain.com
FRONTEND_URL=http://localhost:5173

# Rate limiting settings (requests per minute for login attempts)
LOGIN_RATE_LIMIT=5

# Session settings
SESSION_PERMANENT=True
SESSION_LIFETIME=86400

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================

# Flask server host and port (for development)
FLASK_HOST=127.0.0.1
FLASK_PORT=5000

# Gunicorn configuration (for production)
GUNICORN_WORKERS=2
GUNICORN_BIND=127.0.0.1:5000
GUNICORN_TIMEOUT=30

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Log file path (relative to application root)
LOG_FILE=logs/personal_organizer.log

# =============================================================================
# CLOUDFLARE TUNNEL CONFIGURATION (Optional)
# =============================================================================

# Cloudflare Tunnel token (if using Cloudflare Tunnel for HTTPS)
# CLOUDFLARE_TUNNEL_TOKEN=your-tunnel-token-here

# Public hostname for your application
# PUBLIC_HOSTNAME=your-app.your-domain.com

# =============================================================================
# BACKUP CONFIGURATION (Optional)
# =============================================================================

# Backup directory for MongoDB dumps
BACKUP_DIR=/var/backups/personal-organizer

# Backup retention (days)
BACKUP_RETENTION_DAYS=30

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Enable Flask development features (auto-reload, debug toolbar)
FLASK_DEBUG=False

# Enable detailed error pages
FLASK_TESTING=False

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================

# Weather cache duration (seconds) - 2 hours = 7200
WEATHER_CACHE_DURATION=7200

# Database connection pool settings
DB_MAX_POOL_SIZE=10
DB_MIN_POOL_SIZE=1

# Request timeout for external APIs (seconds)
API_TIMEOUT=10
