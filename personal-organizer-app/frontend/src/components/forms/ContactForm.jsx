import React from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import { Formik, Form, Field, ErrorMessage } from 'formik'
import * as Yup from 'yup'
import { X, User, Phone, MessageCircle } from 'lucide-react'
import { contactsService } from '../../services/api'
import LoadingSpinner from '../ui/LoadingSpinner'
import { format } from 'date-fns'

const ContactSchema = Yup.object().shape({
  name: Yup.string()
    .min(1, 'Name is required')
    .max(100, 'Name must be less than 100 characters')
    .required('Name is required'),
  type_id: Yup.string()
    .nullable(),
  last_contact: Yup.date()
    .nullable()
    .typeError('Invalid date format'),
  notes: Yup.string()
    .max(500, 'Notes must be less than 500 characters'),
})

const ContactForm = ({ contact, onClose, onSuccess }) => {
  const isEditing = !!contact

  const { data: contactTypesData } = useQuery({
    queryKey: ['contact-types'],
    queryFn: contactsService.getContactTypes,
  })

  const createMutation = useMutation({
    mutationFn: contactsService.createContact,
    onSuccess: () => {
      onSuccess?.()
    },
  })

  const updateMutation = useMutation({
    mutationFn: ({ id, ...data }) => contactsService.updateContact(id, data),
    onSuccess: () => {
      onSuccess?.()
    },
  })

  const handleSubmit = (values, { setFieldError }) => {
    const submitData = {
      ...values,
      type_id: values.type_id || null,
      last_contact: values.last_contact || null,
      notes: values.notes || null,
    }

    if (isEditing) {
      updateMutation.mutate({ id: contact.id, ...submitData }, {
        onError: (error) => {
          if (error?.error) {
            setFieldError('name', error.error)
          }
        }
      })
    } else {
      createMutation.mutate(submitData, {
        onError: (error) => {
          if (error?.error) {
            setFieldError('name', error.error)
          }
        }
      })
    }
  }

  const initialValues = {
    name: contact?.name || '',
    type_id: contact?.type_id || '',
    last_contact: contact?.last_contact ? format(new Date(contact.last_contact), 'yyyy-MM-dd') : '',
    notes: contact?.notes || '',
  }

  const contactTypes = contactTypesData?.contact_types || []
  const mutation = isEditing ? updateMutation : createMutation

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />

        <div className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                {isEditing ? 'Edit Contact' : 'Add Contact'}
              </h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <Formik
              initialValues={initialValues}
              validationSchema={ContactSchema}
              onSubmit={handleSubmit}
            >
              {({ isSubmitting, errors, touched, values, setFieldValue }) => (
                <Form className="space-y-4">
                  <div>
                    <label htmlFor="name" className="form-label">
                      Name *
                    </label>
                    <Field
                      id="name"
                      name="name"
                      type="text"
                      placeholder="Contact name"
                      className={`form-input ${
                        errors.name && touched.name
                          ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                          : ''
                      }`}
                    />
                    <ErrorMessage
                      name="name"
                      component="div"
                      className="mt-1 text-sm text-red-600 dark:text-red-400"
                    />
                  </div>

                  <div>
                    <label htmlFor="type_id" className="form-label">
                      Contact Type
                    </label>
                    <Field
                      as="select"
                      id="type_id"
                      name="type_id"
                      className={`form-input ${
                        errors.type_id && touched.type_id
                          ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                          : ''
                      }`}
                    >
                      <option value="">Select type</option>
                      {contactTypes.map((type) => (
                        <option key={type.id} value={type.id}>
                          {type.label} ({type.threshold_days} days)
                        </option>
                      ))}
                    </Field>
                    <p className="mt-1 text-xs text-gray-500">
                      Contact type determines the follow-up threshold
                    </p>
                    <ErrorMessage
                      name="type_id"
                      component="div"
                      className="mt-1 text-sm text-red-600 dark:text-red-400"
                    />
                  </div>

                  <div>
                    <label htmlFor="last_contact" className="form-label">
                      Last Contact Date
                    </label>
                    <Field
                      id="last_contact"
                      name="last_contact"
                      type="date"
                      className={`form-input ${
                        errors.last_contact && touched.last_contact
                          ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                          : ''
                      }`}
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Leave empty to use current date
                    </p>
                    <ErrorMessage
                      name="last_contact"
                      component="div"
                      className="mt-1 text-sm text-red-600 dark:text-red-400"
                    />
                  </div>

                  <div>
                    <label htmlFor="notes" className="form-label">
                      Notes
                    </label>
                    <Field
                      as="textarea"
                      id="notes"
                      name="notes"
                      rows={3}
                      placeholder="Additional notes about this contact..."
                      className={`form-input ${
                        errors.notes && touched.notes
                          ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                          : ''
                      }`}
                    />
                    <ErrorMessage
                      name="notes"
                      component="div"
                      className="mt-1 text-sm text-red-600 dark:text-red-400"
                    />
                  </div>

                  {mutation.isError && (
                    <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
                      <p className="text-sm text-red-600 dark:text-red-400">
                        {mutation.error?.error || 'Failed to save contact. Please try again.'}
                      </p>
                    </div>
                  )}

                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={onClose}
                      className="btn-secondary"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting || mutation.isPending}
                      className="btn-primary"
                    >
                      {mutation.isPending ? (
                        <>
                          <LoadingSpinner size="sm" className="mr-2" />
                          {isEditing ? 'Updating...' : 'Creating...'}
                        </>
                      ) : (
                        <>
                          <User className="mr-2 h-4 w-4" />
                          {isEditing ? 'Update Contact' : 'Create Contact'}
                        </>
                      )}
                    </button>
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ContactForm
