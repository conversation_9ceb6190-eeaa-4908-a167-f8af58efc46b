import React from 'react'
import { useMutation, useQuery } from '@tanstack/react-query'
import { Formik, Form, Field, ErrorMessage } from 'formik'
import * as Yup from 'yup'
import { X, DollarSign, Calendar, Repeat } from 'lucide-react'
import { billsService } from '../../services/api'
import LoadingSpinner from '../ui/LoadingSpinner'
import { format } from 'date-fns'

const BillSchema = Yup.object().shape({
  title: Yup.string()
    .min(1, 'Title is required')
    .max(100, 'Title must be less than 100 characters')
    .required('Title is required'),
  due_date: Yup.date()
    .nullable()
    .typeError('Invalid date format'),
  amount: Yup.number()
    .nullable()
    .positive('Amount must be positive')
    .max(999999, 'Amount too large'),
  category: Yup.string()
    .max(50, 'Category must be less than 50 characters'),
  notes: Yup.string()
    .max(500, 'Notes must be less than 500 characters'),
  rrule: Yup.string()
    .max(200, 'Recurrence rule too long'),
})

const BillForm = ({ bill, onClose, onSuccess }) => {
  const isEditing = !!bill

  const { data: categoriesData } = useQuery({
    queryKey: ['bills', 'categories'],
    queryFn: billsService.getCategories,
  })

  const createMutation = useMutation({
    mutationFn: billsService.createBill,
    onSuccess: () => {
      onSuccess?.()
    },
  })

  const updateMutation = useMutation({
    mutationFn: ({ id, ...data }) => billsService.updateBill(id, data),
    onSuccess: () => {
      onSuccess?.()
    },
  })

  const handleSubmit = (values, { setFieldError }) => {
    const submitData = {
      ...values,
      due_date: values.due_date || null,
      amount: values.amount || null,
      category: values.category || null,
      notes: values.notes || null,
      rrule: values.rrule || null,
    }

    if (isEditing) {
      updateMutation.mutate({ id: bill.id, ...submitData }, {
        onError: (error) => {
          if (error?.error) {
            setFieldError('title', error.error)
          }
        }
      })
    } else {
      createMutation.mutate(submitData, {
        onError: (error) => {
          if (error?.error) {
            setFieldError('title', error.error)
          }
        }
      })
    }
  }

  const initialValues = {
    title: bill?.title || '',
    due_date: bill?.due_date ? format(new Date(bill.due_date), 'yyyy-MM-dd') : '',
    amount: bill?.amount || '',
    category: bill?.category || '',
    notes: bill?.notes || '',
    rrule: bill?.rrule || '',
  }

  const categories = categoriesData?.categories || []
  const mutation = isEditing ? updateMutation : createMutation

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />

        <div className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                {isEditing ? 'Edit Bill' : 'Add Bill or Vacation'}
              </h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <Formik
              initialValues={initialValues}
              validationSchema={BillSchema}
              onSubmit={handleSubmit}
            >
              {({ isSubmitting, errors, touched, values, setFieldValue }) => (
                <Form className="space-y-4">
                  <div>
                    <label htmlFor="title" className="form-label">
                      Title *
                    </label>
                    <Field
                      id="title"
                      name="title"
                      type="text"
                      placeholder="e.g., Mortgage, Hawaii Vacation, Electric Bill"
                      className={`form-input ${
                        errors.title && touched.title
                          ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                          : ''
                      }`}
                    />
                    <ErrorMessage
                      name="title"
                      component="div"
                      className="mt-1 text-sm text-red-600 dark:text-red-400"
                    />
                  </div>

                  <div>
                    <label htmlFor="due_date" className="form-label">
                      Due Date
                    </label>
                    <Field
                      id="due_date"
                      name="due_date"
                      type="date"
                      className={`form-input ${
                        errors.due_date && touched.due_date
                          ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                          : ''
                      }`}
                    />
                    <ErrorMessage
                      name="due_date"
                      component="div"
                      className="mt-1 text-sm text-red-600 dark:text-red-400"
                    />
                  </div>

                  <div>
                    <label htmlFor="amount" className="form-label">
                      Amount
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <DollarSign className="h-4 w-4 text-gray-400" />
                      </div>
                      <Field
                        id="amount"
                        name="amount"
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        className={`form-input pl-10 ${
                          errors.amount && touched.amount
                            ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                            : ''
                        }`}
                      />
                    </div>
                    <ErrorMessage
                      name="amount"
                      component="div"
                      className="mt-1 text-sm text-red-600 dark:text-red-400"
                    />
                  </div>

                  <div>
                    <label htmlFor="category" className="form-label">
                      Category
                    </label>
                    <Field
                      as="select"
                      id="category"
                      name="category"
                      className={`form-input ${
                        errors.category && touched.category
                          ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                          : ''
                      }`}
                    >
                      <option value="">Select category</option>
                      {categories.map((category) => (
                        <option key={category} value={category}>
                          {category}
                        </option>
                      ))}
                    </Field>
                    <ErrorMessage
                      name="category"
                      component="div"
                      className="mt-1 text-sm text-red-600 dark:text-red-400"
                    />
                  </div>

                  <div>
                    <label htmlFor="rrule" className="form-label">
                      Recurrence Rule (RRULE)
                    </label>
                    <Field
                      id="rrule"
                      name="rrule"
                      type="text"
                      placeholder="e.g., FREQ=MONTHLY;INTERVAL=1"
                      className={`form-input ${
                        errors.rrule && touched.rrule
                          ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                          : ''
                      }`}
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Optional. Use RFC 5545 RRULE format for recurring bills.
                    </p>
                    <ErrorMessage
                      name="rrule"
                      component="div"
                      className="mt-1 text-sm text-red-600 dark:text-red-400"
                    />
                  </div>

                  <div>
                    <label htmlFor="notes" className="form-label">
                      Notes
                    </label>
                    <Field
                      as="textarea"
                      id="notes"
                      name="notes"
                      rows={3}
                      placeholder="Additional notes..."
                      className={`form-input ${
                        errors.notes && touched.notes
                          ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                          : ''
                      }`}
                    />
                    <ErrorMessage
                      name="notes"
                      component="div"
                      className="mt-1 text-sm text-red-600 dark:text-red-400"
                    />
                  </div>

                  {mutation.isError && (
                    <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
                      <p className="text-sm text-red-600 dark:text-red-400">
                        {mutation.error?.error || 'Failed to save bill. Please try again.'}
                      </p>
                    </div>
                  )}

                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={onClose}
                      className="btn-secondary"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting || mutation.isPending}
                      className="btn-primary"
                    >
                      {mutation.isPending ? (
                        <>
                          <LoadingSpinner size="sm" className="mr-2" />
                          {isEditing ? 'Updating...' : 'Creating...'}
                        </>
                      ) : (
                        <>
                          <DollarSign className="mr-2 h-4 w-4" />
                          {isEditing ? 'Update Bill' : 'Create Bill'}
                        </>
                      )}
                    </button>
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BillForm
