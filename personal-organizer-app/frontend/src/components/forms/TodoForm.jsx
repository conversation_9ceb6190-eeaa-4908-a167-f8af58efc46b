import React from 'react'
import { useMutation } from '@tanstack/react-query'
import { Formik, Form, Field, ErrorMessage } from 'formik'
import * as Yup from 'yup'
import { X, CheckSquare } from 'lucide-react'
import { todosService } from '../../services/api'
import LoadingSpinner from '../ui/LoadingSpinner'

const TodoSchema = Yup.object().shape({
  text: Yup.string()
    .min(1, 'Todo text is required')
    .max(500, 'Todo text must be less than 500 characters')
    .required('Todo text is required'),
})

const TodoForm = ({ todo, onClose, onSuccess }) => {
  const isEditing = !!todo

  const createMutation = useMutation({
    mutationFn: todosService.createTodo,
    onSuccess: () => {
      onSuccess?.()
    },
  })

  const updateMutation = useMutation({
    mutationFn: ({ id, ...data }) => todosService.updateTodo(id, data),
    onSuccess: () => {
      onSuccess?.()
    },
  })

  const handleSubmit = (values, { setFieldError }) => {
    const submitData = {
      text: values.text.trim(),
    }

    if (isEditing) {
      updateMutation.mutate({ id: todo.id, ...submitData }, {
        onError: (error) => {
          if (error?.error) {
            setFieldError('text', error.error)
          }
        }
      })
    } else {
      createMutation.mutate(submitData, {
        onError: (error) => {
          if (error?.error) {
            setFieldError('text', error.error)
          }
        }
      })
    }
  }

  const initialValues = {
    text: todo?.text || '',
  }

  const mutation = isEditing ? updateMutation : createMutation

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />

        <div className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                {isEditing ? 'Edit Todo' : 'Add Todo'}
              </h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <Formik
              initialValues={initialValues}
              validationSchema={TodoSchema}
              onSubmit={handleSubmit}
            >
              {({ isSubmitting, errors, touched, values }) => (
                <Form className="space-y-4">
                  <div>
                    <label htmlFor="text" className="form-label">
                      Todo Text *
                    </label>
                    <Field
                      as="textarea"
                      id="text"
                      name="text"
                      rows={4}
                      placeholder="What do you need to do?"
                      className={`form-input ${
                        errors.text && touched.text
                          ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                          : ''
                      }`}
                    />
                    <div className="flex justify-between mt-1">
                      <ErrorMessage
                        name="text"
                        component="div"
                        className="text-sm text-red-600 dark:text-red-400"
                      />
                      <span className="text-xs text-gray-500">
                        {values.text.length}/500
                      </span>
                    </div>
                  </div>

                  {mutation.isError && (
                    <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
                      <p className="text-sm text-red-600 dark:text-red-400">
                        {mutation.error?.error || 'Failed to save todo. Please try again.'}
                      </p>
                    </div>
                  )}

                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={onClose}
                      className="btn-secondary"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting || mutation.isPending}
                      className="btn-primary"
                    >
                      {mutation.isPending ? (
                        <>
                          <LoadingSpinner size="sm" className="mr-2" />
                          {isEditing ? 'Updating...' : 'Creating...'}
                        </>
                      ) : (
                        <>
                          <CheckSquare className="mr-2 h-4 w-4" />
                          {isEditing ? 'Update Todo' : 'Create Todo'}
                        </>
                      )}
                    </button>
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TodoForm
