import React from 'react'
import { useMutation } from '@tanstack/react-query'
import { Formik, Form, Field, ErrorMessage } from 'formik'
import * as Yup from 'yup'
import { X, Calendar, Clock } from 'lucide-react'
import { eventsService } from '../../services/api'
import LoadingSpinner from '../ui/LoadingSpinner'
import { format } from 'date-fns'

const EventSchema = Yup.object().shape({
  title: Yup.string()
    .min(1, 'Title is required')
    .max(100, 'Title must be less than 100 characters')
    .required('Title is required'),
  start: Yup.date()
    .required('Start time is required')
    .typeError('Invalid start time'),
  end: Yup.date()
    .nullable()
    .typeError('Invalid end time')
    .when('start', (start, schema) => {
      return start ? schema.min(start, 'End time must be after start time') : schema
    }),
  notes: Yup.string()
    .max(500, 'Notes must be less than 500 characters'),
  rrule: Yup.string()
    .max(200, 'Recurrence rule too long'),
})

const EventForm = ({ event, onClose, onSuccess }) => {
  const isEditing = !!event

  const createMutation = useMutation({
    mutationFn: eventsService.createEvent,
    onSuccess: () => {
      onSuccess?.()
    },
  })

  const updateMutation = useMutation({
    mutationFn: ({ id, ...data }) => eventsService.updateEvent(id, data),
    onSuccess: () => {
      onSuccess?.()
    },
  })

  const handleSubmit = (values, { setFieldError }) => {
    const submitData = {
      ...values,
      end: values.end || null,
      notes: values.notes || null,
      rrule: values.rrule || null,
    }

    if (isEditing) {
      updateMutation.mutate({ id: event.id, ...submitData }, {
        onError: (error) => {
          if (error?.error) {
            setFieldError('title', error.error)
          }
        }
      })
    } else {
      createMutation.mutate(submitData, {
        onError: (error) => {
          if (error?.error) {
            setFieldError('title', error.error)
          }
        }
      })
    }
  }

  const initialValues = {
    title: event?.title || '',
    start: event?.start ? format(new Date(event.start), "yyyy-MM-dd'T'HH:mm") : '',
    end: event?.end ? format(new Date(event.end), "yyyy-MM-dd'T'HH:mm") : '',
    notes: event?.notes || '',
    rrule: event?.rrule || '',
  }

  const mutation = isEditing ? updateMutation : createMutation

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />

        <div className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                {isEditing ? 'Edit Event' : 'Add Event'}
              </h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <Formik
              initialValues={initialValues}
              validationSchema={EventSchema}
              onSubmit={handleSubmit}
            >
              {({ isSubmitting, errors, touched, values, setFieldValue }) => (
                <Form className="space-y-4">
                  <div>
                    <label htmlFor="title" className="form-label">
                      Title *
                    </label>
                    <Field
                      id="title"
                      name="title"
                      type="text"
                      placeholder="Event title"
                      className={`form-input ${
                        errors.title && touched.title
                          ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                          : ''
                      }`}
                    />
                    <ErrorMessage
                      name="title"
                      component="div"
                      className="mt-1 text-sm text-red-600 dark:text-red-400"
                    />
                  </div>

                  <div>
                    <label htmlFor="start" className="form-label">
                      Start Time *
                    </label>
                    <Field
                      id="start"
                      name="start"
                      type="datetime-local"
                      className={`form-input ${
                        errors.start && touched.start
                          ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                          : ''
                      }`}
                    />
                    <ErrorMessage
                      name="start"
                      component="div"
                      className="mt-1 text-sm text-red-600 dark:text-red-400"
                    />
                  </div>

                  <div>
                    <label htmlFor="end" className="form-label">
                      End Time
                    </label>
                    <Field
                      id="end"
                      name="end"
                      type="datetime-local"
                      className={`form-input ${
                        errors.end && touched.end
                          ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                          : ''
                      }`}
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Leave empty for 1-hour duration
                    </p>
                    <ErrorMessage
                      name="end"
                      component="div"
                      className="mt-1 text-sm text-red-600 dark:text-red-400"
                    />
                  </div>

                  <div>
                    <label htmlFor="rrule" className="form-label">
                      Recurrence Rule (RRULE)
                    </label>
                    <Field
                      id="rrule"
                      name="rrule"
                      type="text"
                      placeholder="e.g., FREQ=WEEKLY;BYDAY=MO,WE,FR"
                      className={`form-input ${
                        errors.rrule && touched.rrule
                          ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                          : ''
                      }`}
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Optional. Use RFC 5545 RRULE format for recurring events.
                    </p>
                    <ErrorMessage
                      name="rrule"
                      component="div"
                      className="mt-1 text-sm text-red-600 dark:text-red-400"
                    />
                  </div>

                  <div>
                    <label htmlFor="notes" className="form-label">
                      Notes
                    </label>
                    <Field
                      as="textarea"
                      id="notes"
                      name="notes"
                      rows={3}
                      placeholder="Additional notes..."
                      className={`form-input ${
                        errors.notes && touched.notes
                          ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                          : ''
                      }`}
                    />
                    <ErrorMessage
                      name="notes"
                      component="div"
                      className="mt-1 text-sm text-red-600 dark:text-red-400"
                    />
                  </div>

                  {mutation.isError && (
                    <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
                      <p className="text-sm text-red-600 dark:text-red-400">
                        {mutation.error?.error || 'Failed to save event. Please try again.'}
                      </p>
                    </div>
                  )}

                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={onClose}
                      className="btn-secondary"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={isSubmitting || mutation.isPending}
                      className="btn-primary"
                    >
                      {mutation.isPending ? (
                        <>
                          <LoadingSpinner size="sm" className="mr-2" />
                          {isEditing ? 'Updating...' : 'Creating...'}
                        </>
                      ) : (
                        <>
                          <Calendar className="mr-2 h-4 w-4" />
                          {isEditing ? 'Update Event' : 'Create Event'}
                        </>
                      )}
                    </button>
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </div>
    </div>
  )
}

export default EventForm
