import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { 
  DollarSign, 
  Calendar, 
  Plus, 
  Edit, 
  Trash2, 
  AlertCircle,
  Plane,
  Home
} from 'lucide-react'
import { billsService } from '../../services/api'
import LoadingSpinner from '../ui/LoadingSpinner'
import BillForm from '../forms/BillForm'
import { format, isAfter, isBefore, addDays } from 'date-fns'

const BillsWidget = ({ data: initialData }) => {
  const [showForm, setShowForm] = useState(false)
  const [editingBill, setEditingBill] = useState(null)
  const queryClient = useQueryClient()

  const { data: billsData, isLoading } = useQuery({
    queryKey: ['bills', 'upcoming'],
    queryFn: () => billsService.getUpcoming(30),
    initialData: initialData ? { bills: initialData } : undefined,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })

  const deleteBillMutation = useMutation({
    mutationFn: billsService.deleteBill,
    onSuccess: () => {
      queryClient.invalidateQueries(['bills'])
      queryClient.invalidateQueries(['dashboard'])
    },
  })

  const handleEdit = (bill) => {
    setEditingBill(bill)
    setShowForm(true)
  }

  const handleDelete = (billId) => {
    if (window.confirm('Are you sure you want to delete this bill?')) {
      deleteBillMutation.mutate(billId)
    }
  }

  const handleFormClose = () => {
    setShowForm(false)
    setEditingBill(null)
  }

  const getBillStatus = (dueDate) => {
    const due = new Date(dueDate)
    const now = new Date()
    const tomorrow = addDays(now, 1)
    const nextWeek = addDays(now, 7)

    if (isBefore(due, now)) {
      return { status: 'overdue', color: 'text-red-600 dark:text-red-400', bg: 'bg-red-50 dark:bg-red-900/20' }
    } else if (isBefore(due, tomorrow)) {
      return { status: 'due-today', color: 'text-orange-600 dark:text-orange-400', bg: 'bg-orange-50 dark:bg-orange-900/20' }
    } else if (isBefore(due, nextWeek)) {
      return { status: 'due-soon', color: 'text-yellow-600 dark:text-yellow-400', bg: 'bg-yellow-50 dark:bg-yellow-900/20' }
    } else {
      return { status: 'upcoming', color: 'text-green-600 dark:text-green-400', bg: 'bg-green-50 dark:bg-green-900/20' }
    }
  }

  const isVacation = (bill) => {
    const title = bill.title?.toLowerCase() || ''
    return title.includes('vacation') || title.includes('trip') || title.includes('travel') || 
           title.includes('hawaii') || title.includes('ghana') || title.includes('flight')
  }

  const bills = billsData?.bills || []
  const sortedBills = bills.sort((a, b) => new Date(a.due_date) - new Date(b.due_date))

  return (
    <>
      <div className="widget">
        <div className="widget-header">
          <div className="flex items-center">
            <DollarSign className="h-5 w-5 text-gray-500 mr-2" />
            <h3 className="widget-title">Bills & Vacations</h3>
          </div>
          <button
            onClick={() => setShowForm(true)}
            className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded"
            title="Add bill or vacation"
          >
            <Plus className="h-4 w-4" />
          </button>
        </div>

        <div className="widget-content">
          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <LoadingSpinner size="md" />
            </div>
          ) : bills.length === 0 ? (
            <div className="text-center py-8">
              <DollarSign className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">No upcoming bills or vacations</p>
              <button
                onClick={() => setShowForm(true)}
                className="mt-2 text-sm text-primary-600 hover:text-primary-700"
              >
                Add your first bill
              </button>
            </div>
          ) : (
            <div className="space-y-2">
              {sortedBills.slice(0, 10).map((bill) => {
                const status = getBillStatus(bill.due_date)
                const vacation = isVacation(bill)
                
                return (
                  <div
                    key={bill.id}
                    className={`p-3 rounded-lg border ${status.bg} border-gray-200 dark:border-gray-700`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          {vacation ? (
                            <Plane className="h-4 w-4 text-blue-500" />
                          ) : (
                            <Home className="h-4 w-4 text-gray-500" />
                          )}
                          <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {bill.title}
                          </h4>
                        </div>
                        
                        <div className="flex items-center mt-1 space-x-3">
                          <div className="flex items-center text-xs text-gray-600 dark:text-gray-400">
                            <Calendar className="h-3 w-3 mr-1" />
                            {format(new Date(bill.due_date), 'MMM d, yyyy')}
                          </div>
                          
                          {bill.amount && (
                            <div className="text-xs font-medium text-gray-900 dark:text-white">
                              ${bill.amount.toFixed(2)}
                            </div>
                          )}
                          
                          {bill.category && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                              {bill.category}
                            </span>
                          )}
                        </div>

                        <div className={`text-xs font-medium mt-1 ${status.color}`}>
                          {status.status === 'overdue' && (
                            <div className="flex items-center">
                              <AlertCircle className="h-3 w-3 mr-1" />
                              Overdue
                            </div>
                          )}
                          {status.status === 'due-today' && 'Due Today'}
                          {status.status === 'due-soon' && 'Due This Week'}
                          {status.status === 'upcoming' && 'Upcoming'}
                        </div>
                      </div>

                      <div className="flex items-center space-x-1 ml-2">
                        <button
                          onClick={() => handleEdit(bill)}
                          className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded"
                          title="Edit bill"
                        >
                          <Edit className="h-3 w-3" />
                        </button>
                        <button
                          onClick={() => handleDelete(bill.id)}
                          disabled={deleteBillMutation.isPending}
                          className="p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded disabled:opacity-50"
                          title="Delete bill"
                        >
                          <Trash2 className="h-3 w-3" />
                        </button>
                      </div>
                    </div>
                  </div>
                )
              })}

              {bills.length > 10 && (
                <div className="text-center pt-2">
                  <p className="text-xs text-gray-500">
                    Showing 10 of {bills.length} upcoming bills
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Bill Form Modal */}
      {showForm && (
        <BillForm
          bill={editingBill}
          onClose={handleFormClose}
          onSuccess={() => {
            handleFormClose()
            queryClient.invalidateQueries(['bills'])
            queryClient.invalidateQueries(['dashboard'])
          }}
        />
      )}
    </>
  )
}

export default BillsWidget
