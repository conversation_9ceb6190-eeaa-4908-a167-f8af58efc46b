import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { FixedSizeList as List } from 'react-window'
import { 
  CheckSquare, 
  Plus, 
  Square, 
  Edit, 
  Trash2,
  Check
} from 'lucide-react'
import { todosService } from '../../services/api'
import LoadingSpinner from '../ui/LoadingSpinner'
import TodoForm from '../forms/TodoForm'
import { format } from 'date-fns'

const TodosWidget = ({ data: initialData }) => {
  const [showForm, setShowForm] = useState(false)
  const [editingTodo, setEditingTodo] = useState(null)
  const [quickAddText, setQuickAddText] = useState('')
  const queryClient = useQueryClient()

  const { data: todosData, isLoading } = useQuery({
    queryKey: ['todos', 'active'],
    queryFn: () => todosService.getTodos(1, 50, false),
    initialData: initialData ? { todos: initialData.items, pagination: { total: initialData.total_count } } : undefined,
    staleTime: 1 * 60 * 1000, // 1 minute
  })

  const createTodoMutation = useMutation({
    mutationFn: todosService.createTodo,
    onSuccess: () => {
      queryClient.invalidateQueries(['todos'])
      queryClient.invalidateQueries(['dashboard'])
      setQuickAddText('')
    },
  })

  const completeTodoMutation = useMutation({
    mutationFn: todosService.completeTodo,
    onSuccess: () => {
      queryClient.invalidateQueries(['todos'])
      queryClient.invalidateQueries(['dashboard'])
    },
  })

  const deleteTodoMutation = useMutation({
    mutationFn: todosService.deleteTodo,
    onSuccess: () => {
      queryClient.invalidateQueries(['todos'])
      queryClient.invalidateQueries(['dashboard'])
    },
  })

  const handleQuickAdd = (e) => {
    e.preventDefault()
    if (quickAddText.trim()) {
      createTodoMutation.mutate({ text: quickAddText.trim() })
    }
  }

  const handleComplete = (todoId) => {
    completeTodoMutation.mutate(todoId)
  }

  const handleEdit = (todo) => {
    setEditingTodo(todo)
    setShowForm(true)
  }

  const handleDelete = (todoId) => {
    if (window.confirm('Are you sure you want to delete this todo?')) {
      deleteTodoMutation.mutate(todoId)
    }
  }

  const handleFormClose = () => {
    setShowForm(false)
    setEditingTodo(null)
  }

  const todos = todosData?.todos || []
  const totalCount = todosData?.pagination?.total || 0

  // Todo item component for virtualization
  const TodoItem = ({ index, style }) => {
    const todo = todos[index]
    if (!todo) return null

    return (
      <div style={style} className="px-1">
        <div className="p-2 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 mb-2">
          <div className="flex items-start space-x-2">
            <button
              onClick={() => handleComplete(todo.id)}
              disabled={completeTodoMutation.isPending}
              className="mt-0.5 text-gray-400 hover:text-green-600 dark:hover:text-green-400 disabled:opacity-50"
              title="Mark as complete"
            >
              <Square className="h-4 w-4" />
            </button>
            
            <div className="flex-1 min-w-0">
              <p className="text-sm text-gray-900 dark:text-white break-words">
                {todo.text}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {format(new Date(todo.created_at), 'MMM d, h:mm a')}
              </p>
            </div>

            <div className="flex items-center space-x-1">
              <button
                onClick={() => handleEdit(todo)}
                className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded"
                title="Edit todo"
              >
                <Edit className="h-3 w-3" />
              </button>
              <button
                onClick={() => handleDelete(todo.id)}
                disabled={deleteTodoMutation.isPending}
                className="p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded disabled:opacity-50"
                title="Delete todo"
              >
                <Trash2 className="h-3 w-3" />
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="widget">
        <div className="widget-header">
          <div className="flex items-center">
            <CheckSquare className="h-5 w-5 text-gray-500 mr-2" />
            <h3 className="widget-title">To-dos</h3>
            {totalCount > 0 && (
              <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                {totalCount}
              </span>
            )}
          </div>
          <button
            onClick={() => setShowForm(true)}
            className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded"
            title="Add detailed todo"
          >
            <Plus className="h-4 w-4" />
          </button>
        </div>

        <div className="widget-content">
          {/* Quick Add Form */}
          <form onSubmit={handleQuickAdd} className="mb-3">
            <div className="flex space-x-2">
              <input
                type="text"
                value={quickAddText}
                onChange={(e) => setQuickAddText(e.target.value)}
                placeholder="Quick add todo..."
                className="flex-1 text-sm px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-400 dark:placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500"
                disabled={createTodoMutation.isPending}
              />
              <button
                type="submit"
                disabled={!quickAddText.trim() || createTodoMutation.isPending}
                className="px-3 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
                title="Add todo"
              >
                {createTodoMutation.isPending ? (
                  <LoadingSpinner size="sm" />
                ) : (
                  <Plus className="h-4 w-4" />
                )}
              </button>
            </div>
          </form>

          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <LoadingSpinner size="md" />
            </div>
          ) : todos.length === 0 ? (
            <div className="text-center py-8">
              <CheckSquare className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">No active todos</p>
              <p className="text-xs text-gray-400 mt-1">Add one above to get started</p>
            </div>
          ) : (
            <div className="h-64">
              {todos.length <= 10 ? (
                // Regular list for small number of items
                <div className="space-y-2 max-h-full overflow-y-auto scrollbar-thin">
                  {todos.map((todo, index) => (
                    <TodoItem key={todo.id} index={index} style={{}} />
                  ))}
                </div>
              ) : (
                // Virtualized list for large number of items
                <List
                  height={256}
                  itemCount={todos.length}
                  itemSize={70}
                  className="scrollbar-thin"
                >
                  {TodoItem}
                </List>
              )}
              
              {totalCount > todos.length && (
                <div className="text-center pt-2 border-t border-gray-200 dark:border-gray-700 mt-2">
                  <p className="text-xs text-gray-500">
                    Showing {todos.length} of {totalCount} todos
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Todo Form Modal */}
      {showForm && (
        <TodoForm
          todo={editingTodo}
          onClose={handleFormClose}
          onSuccess={() => {
            handleFormClose()
            queryClient.invalidateQueries(['todos'])
            queryClient.invalidateQueries(['dashboard'])
          }}
        />
      )}
    </>
  )
}

export default TodosWidget
