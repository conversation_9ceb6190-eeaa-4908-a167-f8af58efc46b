import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { 
  Calendar, 
  Plus, 
  Edit, 
  Trash2, 
  Clock,
  MapPin
} from 'lucide-react'
import { eventsService } from '../../services/api'
import LoadingSpinner from '../ui/LoadingSpinner'
import EventForm from '../forms/EventForm'
import { format, isToday, isTomorrow, isThisWeek } from 'date-fns'

const EventsWidget = ({ data: initialData }) => {
  const [showForm, setShowForm] = useState(false)
  const [editingEvent, setEditingEvent] = useState(null)
  const queryClient = useQueryClient()

  const { data: eventsData, isLoading } = useQuery({
    queryKey: ['events', 'upcoming'],
    queryFn: () => eventsService.getUpcoming(7),
    initialData: initialData ? { events: initialData } : undefined,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })

  const deleteEventMutation = useMutation({
    mutationFn: eventsService.deleteEvent,
    onSuccess: () => {
      queryClient.invalidateQueries(['events'])
      queryClient.invalidateQueries(['dashboard'])
    },
  })

  const handleEdit = (event) => {
    setEditingEvent(event)
    setShowForm(true)
  }

  const handleDelete = (eventId) => {
    if (window.confirm('Are you sure you want to delete this event?')) {
      deleteEventMutation.mutate(eventId)
    }
  }

  const handleFormClose = () => {
    setShowForm(false)
    setEditingEvent(null)
  }

  const getEventTimeLabel = (startDate) => {
    const date = new Date(startDate)
    
    if (isToday(date)) {
      return 'Today'
    } else if (isTomorrow(date)) {
      return 'Tomorrow'
    } else if (isThisWeek(date)) {
      return format(date, 'EEEE')
    } else {
      return format(date, 'MMM d')
    }
  }

  const getEventTimeClass = (startDate) => {
    const date = new Date(startDate)
    
    if (isToday(date)) {
      return 'text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20'
    } else if (isTomorrow(date)) {
      return 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20'
    } else {
      return 'text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700'
    }
  }

  const events = eventsData?.events || []
  const sortedEvents = events.sort((a, b) => new Date(a.start) - new Date(b.start))

  return (
    <>
      <div className="widget">
        <div className="widget-header">
          <div className="flex items-center">
            <Calendar className="h-5 w-5 text-gray-500 mr-2" />
            <h3 className="widget-title">Upcoming Events</h3>
          </div>
          <button
            onClick={() => setShowForm(true)}
            className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded"
            title="Add event"
          >
            <Plus className="h-4 w-4" />
          </button>
        </div>

        <div className="widget-content">
          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <LoadingSpinner size="md" />
            </div>
          ) : events.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">No upcoming events</p>
              <button
                onClick={() => setShowForm(true)}
                className="mt-2 text-sm text-primary-600 hover:text-primary-700"
              >
                Add your first event
              </button>
            </div>
          ) : (
            <div className="space-y-2">
              {sortedEvents.slice(0, 8).map((event) => (
                <div
                  key={event.id}
                  className="p-3 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {event.title}
                      </h4>
                      
                      <div className="flex items-center mt-1 space-x-3">
                        <div className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getEventTimeClass(event.start)}`}>
                          <Clock className="h-3 w-3 mr-1" />
                          {getEventTimeLabel(event.start)}
                        </div>
                        
                        <div className="text-xs text-gray-600 dark:text-gray-400">
                          {format(new Date(event.start), 'h:mm a')}
                          {event.end && (
                            <span> - {format(new Date(event.end), 'h:mm a')}</span>
                          )}
                        </div>
                      </div>

                      {event.notes && (
                        <p className="text-xs text-gray-500 mt-1 truncate">
                          {event.notes}
                        </p>
                      )}

                      {event.is_instance && (
                        <div className="text-xs text-gray-400 mt-1">
                          <span className="inline-flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            Recurring event
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center space-x-1 ml-2">
                      <button
                        onClick={() => handleEdit(event)}
                        className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded"
                        title="Edit event"
                      >
                        <Edit className="h-3 w-3" />
                      </button>
                      <button
                        onClick={() => handleDelete(event.id)}
                        disabled={deleteEventMutation.isPending}
                        className="p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded disabled:opacity-50"
                        title="Delete event"
                      >
                        <Trash2 className="h-3 w-3" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}

              {events.length > 8 && (
                <div className="text-center pt-2">
                  <p className="text-xs text-gray-500">
                    Showing 8 of {events.length} upcoming events
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Event Form Modal */}
      {showForm && (
        <EventForm
          event={editingEvent}
          onClose={handleFormClose}
          onSuccess={() => {
            handleFormClose()
            queryClient.invalidateQueries(['events'])
            queryClient.invalidateQueries(['dashboard'])
          }}
        />
      )}
    </>
  )
}

export default EventsWidget
