import React from 'react'
import { useQuery } from '@tanstack/react-query'
import { 
  Cloud, 
  Sun, 
  CloudRain, 
  CloudSnow, 
  Zap, 
  Eye,
  RefreshCw,
  MapPin
} from 'lucide-react'
import { weatherService } from '../../services/api'
import LoadingSpinner from '../ui/LoadingSpinner'
import { format } from 'date-fns'

const WeatherWidget = ({ city, cityName, data: initialData }) => {
  const { data: weatherData, isLoading, error, refetch } = useQuery({
    queryKey: ['weather', city],
    queryFn: () => weatherService.getWeather(city),
    initialData: initialData ? { weather: initialData } : undefined,
    staleTime: 2 * 60 * 60 * 1000, // 2 hours (matches backend cache)
    refetchInterval: 2 * 60 * 60 * 1000, // Refetch every 2 hours
  })

  const getWeatherIcon = (condition, size = 'md') => {
    const sizeClasses = {
      sm: 'h-4 w-4',
      md: 'h-6 w-6',
      lg: 'h-8 w-8',
      xl: 'h-12 w-12'
    }

    const iconClass = sizeClasses[size]
    const lowerCondition = condition?.toLowerCase() || ''

    if (lowerCondition.includes('clear') || lowerCondition.includes('sunny')) {
      return <Sun className={`${iconClass} text-yellow-500`} />
    } else if (lowerCondition.includes('cloud')) {
      return <Cloud className={`${iconClass} text-gray-500`} />
    } else if (lowerCondition.includes('rain') || lowerCondition.includes('drizzle')) {
      return <CloudRain className={`${iconClass} text-blue-500`} />
    } else if (lowerCondition.includes('snow')) {
      return <CloudSnow className={`${iconClass} text-blue-300`} />
    } else if (lowerCondition.includes('thunder')) {
      return <Zap className={`${iconClass} text-purple-500`} />
    } else {
      return <Eye className={`${iconClass} text-gray-400`} />
    }
  }

  const weather = weatherData?.weather

  return (
    <div className="widget">
      <div className="widget-header">
        <div className="flex items-center">
          <MapPin className="h-5 w-5 text-gray-500 mr-2" />
          <h3 className="widget-title">{cityName}</h3>
        </div>
        <button
          onClick={() => refetch()}
          disabled={isLoading}
          className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded"
          title="Refresh weather"
        >
          <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      <div className="widget-content">
        {isLoading && !weather ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <LoadingSpinner size="md" />
              <p className="mt-2 text-sm text-gray-500">Loading weather...</p>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <Cloud className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">Weather unavailable</p>
              <button
                onClick={() => refetch()}
                className="mt-2 text-xs text-primary-600 hover:text-primary-700"
              >
                Try again
              </button>
            </div>
          </div>
        ) : weather ? (
          <div className="space-y-4">
            {/* Current Weather */}
            <div className="text-center">
              <div className="flex items-center justify-center mb-2">
                {getWeatherIcon(weather.current_condition, 'xl')}
              </div>
              <div className="text-3xl font-bold text-gray-900 dark:text-white">
                {weather.current_temp ? `${Math.round(weather.current_temp)}°F` : '--°F'}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {weather.current_condition || 'Unknown'}
              </div>
            </div>

            {/* 3-Day Forecast */}
            {weather.forecast && weather.forecast.length > 0 && (
              <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  3-Day Forecast
                </h4>
                <div className="space-y-2">
                  {weather.forecast.slice(0, 3).map((day, index) => (
                    <div key={day.date} className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-2">
                        {getWeatherIcon(day.condition, 'sm')}
                        <span className="text-gray-600 dark:text-gray-400">
                          {index === 0 ? 'Today' : 
                           index === 1 ? 'Tomorrow' : 
                           format(new Date(day.date), 'EEE')}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-gray-900 dark:text-white">
                          {day.max_temp ? Math.round(day.max_temp) : '--'}°
                        </span>
                        <span className="text-gray-500">
                          {day.min_temp ? Math.round(day.min_temp) : '--'}°
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Last Updated */}
            {weather.fetched_at && (
              <div className="text-xs text-gray-400 text-center pt-2 border-t border-gray-200 dark:border-gray-700">
                Updated {format(new Date(weather.fetched_at), 'h:mm a')}
              </div>
            )}
          </div>
        ) : (
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <Cloud className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">No weather data</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default WeatherWidget
