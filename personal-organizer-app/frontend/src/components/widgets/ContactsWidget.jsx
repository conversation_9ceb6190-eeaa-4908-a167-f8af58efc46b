import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { 
  Users, 
  Plus, 
  MessageCircle, 
  Edit, 
  Trash2,
  Clock,
  Phone
} from 'lucide-react'
import { contactsService } from '../../services/api'
import LoadingSpinner from '../ui/LoadingSpinner'
import ContactForm from '../forms/ContactForm'
import { format, formatDistanceToNow } from 'date-fns'

const ContactsWidget = ({ data: initialData }) => {
  const [showForm, setShowForm] = useState(false)
  const [editingContact, setEditingContact] = useState(null)
  const queryClient = useQueryClient()

  const { data: contactsData, isLoading } = useQuery({
    queryKey: ['contacts', 'status'],
    queryFn: contactsService.getContactsByStatus,
    initialData: initialData ? { status_groups: initialData } : undefined,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })

  const pingContactMutation = useMutation({
    mutationFn: contactsService.pingContact,
    onSuccess: () => {
      queryClient.invalidateQueries(['contacts'])
      queryClient.invalidateQueries(['dashboard'])
    },
  })

  const deleteContactMutation = useMutation({
    mutationFn: contactsService.deleteContact,
    onSuccess: () => {
      queryClient.invalidateQueries(['contacts'])
      queryClient.invalidateQueries(['dashboard'])
    },
  })

  const handlePing = (contactId) => {
    pingContactMutation.mutate(contactId)
  }

  const handleEdit = (contact) => {
    setEditingContact(contact)
    setShowForm(true)
  }

  const handleDelete = (contactId) => {
    if (window.confirm('Are you sure you want to delete this contact?')) {
      deleteContactMutation.mutate(contactId)
    }
  }

  const handleFormClose = () => {
    setShowForm(false)
    setEditingContact(null)
  }

  const getStatusBadgeClass = (color) => {
    switch (color) {
      case 'green':
        return 'status-green'
      case 'yellow':
        return 'status-yellow'
      case 'red':
        return 'status-red'
      default:
        return 'status-gray'
    }
  }

  const getStatusText = (color, thresholdDays, daysSince) => {
    switch (color) {
      case 'green':
        return 'Recent contact'
      case 'yellow':
        return 'Follow up soon'
      case 'red':
        return 'Needs attention'
      default:
        return 'No threshold set'
    }
  }

  const statusGroups = contactsData?.status_groups || { green: [], yellow: [], red: [], gray: [] }
  const allContacts = [...statusGroups.red, ...statusGroups.yellow, ...statusGroups.green, ...statusGroups.gray]
  const totalContacts = allContacts.length

  return (
    <>
      <div className="widget">
        <div className="widget-header">
          <div className="flex items-center">
            <Users className="h-5 w-5 text-gray-500 mr-2" />
            <h3 className="widget-title">Contacts</h3>
          </div>
          <button
            onClick={() => setShowForm(true)}
            className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded"
            title="Add contact"
          >
            <Plus className="h-4 w-4" />
          </button>
        </div>

        <div className="widget-content">
          {isLoading ? (
            <div className="flex items-center justify-center h-32">
              <LoadingSpinner size="md" />
            </div>
          ) : totalContacts === 0 ? (
            <div className="text-center py-8">
              <Users className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">No contacts yet</p>
              <button
                onClick={() => setShowForm(true)}
                className="mt-2 text-sm text-primary-600 hover:text-primary-700"
              >
                Add your first contact
              </button>
            </div>
          ) : (
            <div className="space-y-3">
              {/* Status Summary */}
              <div className="grid grid-cols-4 gap-2 text-center">
                <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-2">
                  <div className="text-lg font-bold text-red-600 dark:text-red-400">
                    {statusGroups.red.length}
                  </div>
                  <div className="text-xs text-red-600 dark:text-red-400">Need Attention</div>
                </div>
                <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-2">
                  <div className="text-lg font-bold text-yellow-600 dark:text-yellow-400">
                    {statusGroups.yellow.length}
                  </div>
                  <div className="text-xs text-yellow-600 dark:text-yellow-400">Follow Up</div>
                </div>
                <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-2">
                  <div className="text-lg font-bold text-green-600 dark:text-green-400">
                    {statusGroups.green.length}
                  </div>
                  <div className="text-xs text-green-600 dark:text-green-400">Recent</div>
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-2">
                  <div className="text-lg font-bold text-gray-600 dark:text-gray-400">
                    {statusGroups.gray.length}
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">Other</div>
                </div>
              </div>

              {/* Contact List */}
              <div className="space-y-2 max-h-64 overflow-y-auto scrollbar-thin">
                {allContacts.slice(0, 15).map((contact) => (
                  <div
                    key={contact.id}
                    className="p-2 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {contact.name}
                          </h4>
                          <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getStatusBadgeClass(contact.status_color)}`}>
                            {getStatusText(contact.status_color, contact.threshold_days, contact.days_since_contact)}
                          </span>
                        </div>
                        
                        <div className="flex items-center mt-1 text-xs text-gray-600 dark:text-gray-400">
                          <Clock className="h-3 w-3 mr-1" />
                          {contact.last_contact ? 
                            `${formatDistanceToNow(new Date(contact.last_contact))} ago` : 
                            'Never contacted'
                          }
                        </div>

                        {contact.type_label && (
                          <div className="text-xs text-gray-500 mt-1">
                            {contact.type_label} • {contact.threshold_days} day threshold
                          </div>
                        )}
                      </div>

                      <div className="flex items-center space-x-1 ml-2">
                        <button
                          onClick={() => handlePing(contact.id)}
                          disabled={pingContactMutation.isPending}
                          className="p-1 text-gray-400 hover:text-green-600 dark:hover:text-green-400 rounded disabled:opacity-50"
                          title="Mark as contacted"
                        >
                          <MessageCircle className="h-3 w-3" />
                        </button>
                        <button
                          onClick={() => handleEdit(contact)}
                          className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded"
                          title="Edit contact"
                        >
                          <Edit className="h-3 w-3" />
                        </button>
                        <button
                          onClick={() => handleDelete(contact.id)}
                          disabled={deleteContactMutation.isPending}
                          className="p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded disabled:opacity-50"
                          title="Delete contact"
                        >
                          <Trash2 className="h-3 w-3" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}

                {allContacts.length > 15 && (
                  <div className="text-center pt-2">
                    <p className="text-xs text-gray-500">
                      Showing 15 of {totalContacts} contacts
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Contact Form Modal */}
      {showForm && (
        <ContactForm
          contact={editingContact}
          onClose={handleFormClose}
          onSuccess={() => {
            handleFormClose()
            queryClient.invalidateQueries(['contacts'])
            queryClient.invalidateQueries(['dashboard'])
          }}
        />
      )}
    </>
  )
}

export default ContactsWidget
