import React, { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Formik, Form, Field, ErrorMessage } from 'formik'
import * as Yup from 'yup'
import { User, Mail, Phone, Lock, Save, Eye, EyeOff } from 'lucide-react'
import { profileService } from '../services/api'
import LoadingSpinner from '../components/ui/LoadingSpinner'

const ProfileSchema = Yup.object().shape({
  username: Yup.string()
    .min(3, 'Username must be at least 3 characters')
    .max(50, 'Username must be less than 50 characters')
    .required('Username is required'),
  email: Yup.string()
    .email('Invalid email format')
    .nullable(),
  phone: Yup.string()
    .nullable(),
  current_password: Yup.string()
    .when('password', {
      is: (password) => password && password.length > 0,
      then: (schema) => schema.required('Current password is required to change password'),
      otherwise: (schema) => schema.nullable(),
    }),
  password: Yup.string()
    .min(6, 'Password must be at least 6 characters')
    .nullable(),
  confirm_password: Yup.string()
    .when('password', {
      is: (password) => password && password.length > 0,
      then: (schema) => schema
        .required('Please confirm your password')
        .oneOf([Yup.ref('password')], 'Passwords must match'),
      otherwise: (schema) => schema.nullable(),
    }),
})

const ProfilePage = () => {
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const queryClient = useQueryClient()

  const { data: profileData, isLoading, error } = useQuery({
    queryKey: ['profile'],
    queryFn: profileService.getProfile,
  })

  const updateProfileMutation = useMutation({
    mutationFn: profileService.updateProfile,
    onSuccess: () => {
      queryClient.invalidateQueries(['profile'])
      queryClient.invalidateQueries(['auth-status'])
    },
  })

  const handleSubmit = (values, { setFieldError, resetForm }) => {
    const submitData = {
      username: values.username,
      email: values.email || null,
      phone: values.phone || null,
    }

    // Only include password fields if password is being changed
    if (values.password) {
      submitData.current_password = values.current_password
      submitData.password = values.password
    }

    updateProfileMutation.mutate(submitData, {
      onSuccess: () => {
        // Reset password fields on success
        resetForm({
          values: {
            ...values,
            current_password: '',
            password: '',
            confirm_password: '',
          }
        })
      },
      onError: (error) => {
        if (error?.error) {
          if (error.error.includes('password')) {
            setFieldError('current_password', error.error)
          } else if (error.error.includes('username')) {
            setFieldError('username', error.error)
          } else {
            setFieldError('username', error.error)
          }
        }
      }
    })
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading profile...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
        <p className="text-red-600 dark:text-red-400">
          Failed to load profile data. Please try again.
        </p>
      </div>
    )
  }

  const profile = profileData?.profile

  const initialValues = {
    username: profile?.username || '',
    email: profile?.email || '',
    phone: profile?.phone || '',
    current_password: '',
    password: '',
    confirm_password: '',
  }

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Profile Settings</h1>
            <p className="text-gray-600 dark:text-gray-400">
              Manage your account information and security settings
            </p>
          </div>

          <Formik
            initialValues={initialValues}
            validationSchema={ProfileSchema}
            onSubmit={handleSubmit}
            enableReinitialize
          >
            {({ isSubmitting, errors, touched, values, setFieldValue }) => (
              <Form className="space-y-6">
                {/* Basic Information */}
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    Basic Information
                  </h3>
                  
                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <label htmlFor="username" className="form-label">
                        Username *
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <User className="h-4 w-4 text-gray-400" />
                        </div>
                        <Field
                          id="username"
                          name="username"
                          type="text"
                          className={`form-input pl-10 ${
                            errors.username && touched.username
                              ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                              : ''
                          }`}
                        />
                      </div>
                      <ErrorMessage
                        name="username"
                        component="div"
                        className="mt-1 text-sm text-red-600 dark:text-red-400"
                      />
                    </div>

                    <div>
                      <label htmlFor="email" className="form-label">
                        Email
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Mail className="h-4 w-4 text-gray-400" />
                        </div>
                        <Field
                          id="email"
                          name="email"
                          type="email"
                          placeholder="<EMAIL>"
                          className={`form-input pl-10 ${
                            errors.email && touched.email
                              ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                              : ''
                          }`}
                        />
                      </div>
                      <ErrorMessage
                        name="email"
                        component="div"
                        className="mt-1 text-sm text-red-600 dark:text-red-400"
                      />
                    </div>

                    <div>
                      <label htmlFor="phone" className="form-label">
                        Phone
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Phone className="h-4 w-4 text-gray-400" />
                        </div>
                        <Field
                          id="phone"
                          name="phone"
                          type="tel"
                          placeholder="(*************"
                          className={`form-input pl-10 ${
                            errors.phone && touched.phone
                              ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                              : ''
                          }`}
                        />
                      </div>
                      <ErrorMessage
                        name="phone"
                        component="div"
                        className="mt-1 text-sm text-red-600 dark:text-red-400"
                      />
                    </div>
                  </div>
                </div>

                {/* Password Change */}
                <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    Change Password
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    Leave password fields empty if you don't want to change your password.
                  </p>
                  
                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <label htmlFor="current_password" className="form-label">
                        Current Password
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Lock className="h-4 w-4 text-gray-400" />
                        </div>
                        <Field
                          id="current_password"
                          name="current_password"
                          type={showCurrentPassword ? 'text' : 'password'}
                          className={`form-input pl-10 pr-10 ${
                            errors.current_password && touched.current_password
                              ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                              : ''
                          }`}
                        />
                        <button
                          type="button"
                          onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        >
                          {showCurrentPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      </div>
                      <ErrorMessage
                        name="current_password"
                        component="div"
                        className="mt-1 text-sm text-red-600 dark:text-red-400"
                      />
                    </div>

                    <div>
                      <label htmlFor="password" className="form-label">
                        New Password
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Lock className="h-4 w-4 text-gray-400" />
                        </div>
                        <Field
                          id="password"
                          name="password"
                          type={showNewPassword ? 'text' : 'password'}
                          className={`form-input pl-10 pr-10 ${
                            errors.password && touched.password
                              ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                              : ''
                          }`}
                        />
                        <button
                          type="button"
                          onClick={() => setShowNewPassword(!showNewPassword)}
                          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        >
                          {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      </div>
                      <ErrorMessage
                        name="password"
                        component="div"
                        className="mt-1 text-sm text-red-600 dark:text-red-400"
                      />
                    </div>

                    <div>
                      <label htmlFor="confirm_password" className="form-label">
                        Confirm New Password
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Lock className="h-4 w-4 text-gray-400" />
                        </div>
                        <Field
                          id="confirm_password"
                          name="confirm_password"
                          type={showConfirmPassword ? 'text' : 'password'}
                          className={`form-input pl-10 pr-10 ${
                            errors.confirm_password && touched.confirm_password
                              ? 'border-red-500 focus:ring-red-500 focus:border-red-500'
                              : ''
                          }`}
                        />
                        <button
                          type="button"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                        >
                          {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      </div>
                      <ErrorMessage
                        name="confirm_password"
                        component="div"
                        className="mt-1 text-sm text-red-600 dark:text-red-400"
                      />
                    </div>
                  </div>
                </div>

                {updateProfileMutation.isError && (
                  <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-3">
                    <p className="text-sm text-red-600 dark:text-red-400">
                      {updateProfileMutation.error?.error || 'Failed to update profile. Please try again.'}
                    </p>
                  </div>
                )}

                {updateProfileMutation.isSuccess && (
                  <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md p-3">
                    <p className="text-sm text-green-600 dark:text-green-400">
                      Profile updated successfully!
                    </p>
                  </div>
                )}

                <div className="flex justify-end">
                  <button
                    type="submit"
                    disabled={isSubmitting || updateProfileMutation.isPending}
                    className="btn-primary"
                  >
                    {updateProfileMutation.isPending ? (
                      <>
                        <LoadingSpinner size="sm" className="mr-2" />
                        Updating...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Changes
                      </>
                    )}
                  </button>
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>
    </div>
  )
}

export default ProfilePage
