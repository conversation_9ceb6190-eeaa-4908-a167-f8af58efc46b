import React, { useState, useCallback } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Responsive, WidthProvider } from 'react-grid-layout'
import { dashboardService } from '../services/api'
import WeatherWidget from '../components/widgets/WeatherWidget'
import BillsWidget from '../components/widgets/BillsWidget'
import ContactsWidget from '../components/widgets/ContactsWidget'
import TodosWidget from '../components/widgets/TodosWidget'
import EventsWidget from '../components/widgets/EventsWidget'
import LoadingSpinner from '../components/ui/LoadingSpinner'
import { RefreshCw, Settings } from 'lucide-react'

const ResponsiveGridLayout = WidthProvider(Responsive)

const Dashboard = () => {
  const [isEditMode, setIsEditMode] = useState(false)
  const queryClient = useQueryClient()

  // Fetch dashboard data
  const { data: dashboardData, isLoading, error, refetch } = useQuery({
    queryKey: ['dashboard'],
    queryFn: dashboardService.getDashboardData,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  })

  // Save layout mutation
  const saveLayoutMutation = useMutation({
    mutationFn: dashboardService.saveLayout,
    onSuccess: () => {
      queryClient.invalidateQueries(['dashboard'])
    },
  })

  const handleLayoutChange = useCallback((layout, layouts) => {
    if (isEditMode) {
      saveLayoutMutation.mutate(layouts)
    }
  }, [isEditMode, saveLayoutMutation])

  const handleRefresh = () => {
    refetch()
    // Also refresh individual widget queries
    queryClient.invalidateQueries(['weather'])
    queryClient.invalidateQueries(['bills'])
    queryClient.invalidateQueries(['contacts'])
    queryClient.invalidateQueries(['todos'])
    queryClient.invalidateQueries(['events'])
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
        <p className="text-red-600 dark:text-red-400">
          Failed to load dashboard data. Please try again.
        </p>
        <button
          onClick={handleRefresh}
          className="mt-2 btn-primary"
        >
          Retry
        </button>
      </div>
    )
  }

  const defaultLayouts = {
    lg: [
      { i: 'weather-stl', x: 0, y: 0, w: 2, h: 3, minW: 2, minH: 3 },
      { i: 'weather-accra', x: 2, y: 0, w: 2, h: 3, minW: 2, minH: 3 },
      { i: 'bills-upcoming', x: 0, y: 3, w: 4, h: 4, minW: 3, minH: 3 },
      { i: 'contacts-status', x: 0, y: 7, w: 2, h: 3, minW: 2, minH: 3 },
      { i: 'todos', x: 2, y: 7, w: 2, h: 3, minW: 2, minH: 3 },
      { i: 'events-upcoming', x: 0, y: 10, w: 4, h: 3, minW: 3, minH: 3 }
    ],
    md: [
      { i: 'weather-stl', x: 0, y: 0, w: 2, h: 3, minW: 2, minH: 3 },
      { i: 'weather-accra', x: 2, y: 0, w: 2, h: 3, minW: 2, minH: 3 },
      { i: 'bills-upcoming', x: 0, y: 3, w: 4, h: 4, minW: 3, minH: 3 },
      { i: 'contacts-status', x: 0, y: 7, w: 2, h: 3, minW: 2, minH: 3 },
      { i: 'todos', x: 2, y: 7, w: 2, h: 3, minW: 2, minH: 3 },
      { i: 'events-upcoming', x: 0, y: 10, w: 4, h: 3, minW: 3, minH: 3 }
    ],
    sm: [
      { i: 'weather-stl', x: 0, y: 0, w: 2, h: 3, minW: 2, minH: 3 },
      { i: 'weather-accra', x: 0, y: 3, w: 2, h: 3, minW: 2, minH: 3 },
      { i: 'bills-upcoming', x: 0, y: 6, w: 2, h: 4, minW: 2, minH: 3 },
      { i: 'contacts-status', x: 0, y: 10, w: 2, h: 3, minW: 2, minH: 3 },
      { i: 'todos', x: 0, y: 13, w: 2, h: 3, minW: 2, minH: 3 },
      { i: 'events-upcoming', x: 0, y: 16, w: 2, h: 3, minW: 2, minH: 3 }
    ]
  }

  const layouts = dashboardData?.layout || defaultLayouts

  return (
    <div className="space-y-6">
      {/* Dashboard Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Dashboard</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Welcome to your personal organizer
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className="btn-secondary"
            title="Refresh dashboard"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
          
          <button
            onClick={() => setIsEditMode(!isEditMode)}
            className={`btn-secondary ${isEditMode ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-200' : ''}`}
            title={isEditMode ? 'Exit edit mode' : 'Edit layout'}
          >
            <Settings className="h-4 w-4" />
            {isEditMode ? 'Done' : 'Edit'}
          </button>
        </div>
      </div>

      {/* Edit Mode Notice */}
      {isEditMode && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-3">
          <p className="text-sm text-blue-600 dark:text-blue-400">
            <Settings className="inline h-4 w-4 mr-1" />
            Edit mode active: Drag and resize widgets to customize your dashboard layout.
          </p>
        </div>
      )}

      {/* Dashboard Grid */}
      <ResponsiveGridLayout
        className="layout"
        layouts={layouts}
        onLayoutChange={handleLayoutChange}
        breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
        cols={{ lg: 4, md: 4, sm: 2, xs: 1, xxs: 1 }}
        rowHeight={80}
        isDraggable={isEditMode}
        isResizable={isEditMode}
        margin={[16, 16]}
        containerPadding={[0, 0]}
        useCSSTransforms={true}
      >
        {/* Weather Widgets */}
        <div key="weather-stl">
          <WeatherWidget 
            city="stl" 
            cityName="St. Louis"
            data={dashboardData?.weather_stl}
          />
        </div>
        
        <div key="weather-accra">
          <WeatherWidget 
            city="accra" 
            cityName="Accra"
            data={dashboardData?.weather_accra}
          />
        </div>

        {/* Bills Widget */}
        <div key="bills-upcoming">
          <BillsWidget data={dashboardData?.upcoming_bills} />
        </div>

        {/* Contacts Widget */}
        <div key="contacts-status">
          <ContactsWidget data={dashboardData?.contact_status} />
        </div>

        {/* Todos Widget */}
        <div key="todos">
          <TodosWidget data={dashboardData?.todos} />
        </div>

        {/* Events Widget */}
        <div key="events-upcoming">
          <EventsWidget data={dashboardData?.upcoming_events} />
        </div>
      </ResponsiveGridLayout>

      {/* Save Status */}
      {saveLayoutMutation.isPending && (
        <div className="fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-md shadow-lg">
          <div className="flex items-center">
            <LoadingSpinner size="sm" className="mr-2" />
            Saving layout...
          </div>
        </div>
      )}
    </div>
  )
}

export default Dashboard
