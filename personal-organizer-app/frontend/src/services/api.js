import axios from 'axios'

// Create axios instance with default config
const api = axios.create({
  baseURL: '/api',
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  (error) => {
    if (error.response?.status === 401) {
      // Redirect to login on 401
      window.location.href = '/login'
    }
    return Promise.reject(error.response?.data || error)
  }
)

// Auth service
export const authService = {
  login: (credentials) => api.post('/auth/login', credentials),
  logout: () => api.post('/auth/logout'),
  checkStatus: () => api.get('/auth/status'),
  register: (userData) => api.post('/auth/register', userData),
}

// Dashboard service
export const dashboardService = {
  getDashboardData: () => api.get('/dashboard'),
  saveLayout: (layout) => api.post('/layout', { layout }),
  getSummary: () => api.get('/summary'),
}

// Bills service
export const billsService = {
  getBills: (includeInstances = true) => api.get(`/bills?include_instances=${includeInstances}`),
  createBill: (billData) => api.post('/bills', billData),
  getBill: (id) => api.get(`/bills/${id}`),
  updateBill: (id, billData) => api.patch(`/bills/${id}`, billData),
  deleteBill: (id) => api.delete(`/bills/${id}`),
  getUpcoming: (days = 30) => api.get(`/bills/upcoming?days=${days}`),
  getCategories: () => api.get('/bills/categories'),
  addCategory: (category) => api.post('/bills/categories', { category }),
  removeCategory: (category) => api.delete(`/bills/categories/${category}`),
}

// Events service
export const eventsService = {
  getEvents: (includeInstances = true) => api.get(`/events?include_instances=${includeInstances}`),
  createEvent: (eventData) => api.post('/events', eventData),
  getEvent: (id) => api.get(`/events/${id}`),
  updateEvent: (id, eventData) => api.patch(`/events/${id}`, eventData),
  deleteEvent: (id) => api.delete(`/events/${id}`),
  getUpcoming: (days = 7) => api.get(`/events/upcoming?days=${days}`),
}

// Contacts service
export const contactsService = {
  getContacts: () => api.get('/contacts'),
  createContact: (contactData) => api.post('/contacts', contactData),
  getContact: (id) => api.get(`/contacts/${id}`),
  updateContact: (id, contactData) => api.patch(`/contacts/${id}`, contactData),
  deleteContact: (id) => api.delete(`/contacts/${id}`),
  pingContact: (id) => api.post(`/contacts/${id}/ping`),
  getContactsByStatus: () => api.get('/contacts/status'),
  getContactTypes: () => api.get('/contact-types'),
  createContactType: (typeData) => api.post('/contact-types', typeData),
  deleteContactType: (id) => api.delete(`/contact-types/${id}`),
}

// Todos service
export const todosService = {
  getTodos: (page = 1, perPage = 50, includeCompleted = false) => 
    api.get(`/todos?page=${page}&per_page=${perPage}&include_completed=${includeCompleted}`),
  createTodo: (todoData) => api.post('/todos', todoData),
  getTodo: (id) => api.get(`/todos/${id}`),
  updateTodo: (id, todoData) => api.patch(`/todos/${id}`, todoData),
  deleteTodo: (id) => api.delete(`/todos/${id}`),
  completeTodo: (id) => api.post(`/todos/${id}/complete`),
  bulkDeleteCompleted: () => api.delete('/todos/bulk-delete-completed'),
  getRecentCompleted: (limit = 10) => api.get(`/todos/recent-completed?limit=${limit}`),
  getStats: () => api.get('/todos/stats'),
}

// Weather service
export const weatherService = {
  getWeather: (city) => api.get(`/weather?city=${city}`),
  refreshWeather: (city) => api.post('/weather/refresh', { city }),
  getAllWeather: () => api.get('/weather/all'),
  getCacheStatus: () => api.get('/weather/cache-status'),
}

// Profile service
export const profileService = {
  getProfile: () => api.get('/profile'),
  updateProfile: (profileData) => api.patch('/profile', profileData),
  getCategories: () => api.get('/profile/categories'),
  addCategory: (category) => api.post('/profile/categories', { category }),
  removeCategory: (category) => api.delete(`/profile/categories/${category}`),
  getDashboardLayout: () => api.get('/profile/dashboard-layout'),
  updateDashboardLayout: (layout) => api.post('/profile/dashboard-layout', { layout }),
}

export default api
