# Personal Organizer App - Project Brief

## Project Overview
Building a world-class, single-user personal organizer application that runs on Ubuntu homelab with production-grade design. The app consolidates bills, expenses, appointments, weather, contacts, and to-dos into a unified drag-and-drop dashboard.

## Core Requirements

### Authentication
- Clean, minimalist "Welcome Back" login page
- Username + password fields with show/hide toggle
- Single-user system (no signup/reset options)
- Flask-Login session management
- Flask-Limiter: 5 attempts per minute protection

### Tech Stack (Non-negotiable)
**Backend:**
- Python 3.x with Flask
- Flask-Login for authentication
- Flask-PyMongo for MongoDB integration
- python-decouple for environment variables
- Gunicorn-ready configuration

**Database:**
- MongoDB as primary datastore
- Complete schema/models with TTL indexes

**Frontend:**
- React with functional components and hooks
- Vite as build tool
- Modern, responsive UI
- React-Grid-Layout for dashboard
- react-window for virtualization

**Configuration:**
- All secrets in .env file
- systemd service file
- Cloudflare Tunnel deployment ready

### Core Features
1. **Dashboard**: Four-column grid with drag-and-drop widgets
2. **Bills & Vacations**: RRULE recurrence, full CRUD operations
3. **Appointments & Activities**: Same RRULE engine, separate collection
4. **Contacts**: Color-coded thresholds (3, 7, 21 days)
5. **Unlimited To-dos**: Virtualized list for performance
6. **Weather**: St Louis & Accra, Fahrenheit, 2-hour cache
7. **Profile Management**: Username, password, email, phone updates

### Key Technical Requirements
- All dates stored in UTC, rendered in America/Chicago timezone
- Hard deletes for storage efficiency
- Weather cache with 2-hour refresh cycle
- Production-ready code with proper error handling
- CORS configuration for frontend communication
- Input validation and security measures
- Complete API documentation

## Success Criteria
- Zero placeholders or TODO comments
- All files complete and functional
- Production-ready deployment capability
- Comprehensive testing with Playwright
- Clean, responsive UI matching specifications

## Deliverables
- Complete backend Flask application
- Complete React frontend with Vite
- MongoDB models and schemas
- Configuration files (.env.example, systemd, requirements.txt, package.json)
- Comprehensive README.md with setup instructions
- All files ready for immediate deployment
