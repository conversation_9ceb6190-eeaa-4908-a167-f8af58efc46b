# Technical Context - Personal Organizer App

## Technology Stack

### Backend Technologies
- **Python 3.9+**: Core runtime environment
- **Flask 3.x**: Web framework with blueprint organization
- **Flask-Login**: Session-based authentication management
- **Flask-PyMongo**: MongoDB integration with Flask
- **Flask-Limiter**: Rate limiting for security
- **python-decouple**: Environment variable management
- **python-dateutil**: RRULE recurrence processing
- **Gunicorn**: WSGI HTTP server for production
- **Werkzeug**: Password hashing and security utilities

### Database Technology
- **MongoDB Community**: Document database with flexible schema
- **PyMongo**: Python driver for MongoDB operations
- **TTL Indexes**: Automatic document expiration
- **Aggregation Pipeline**: Complex queries and data processing

### Frontend Technologies
- **React 18**: Component-based UI framework
- **Vite**: Fast build tool and development server
- **React-Grid-Layout**: Drag-and-drop dashboard widgets
- **react-window**: List virtualization for performance
- **React Query**: Server state management and caching
- **Tailwind CSS**: Utility-first CSS framework
- **Lucide React**: Icon library for UI elements
- **Formik + Yup**: Form handling and validation

### Development Setup

#### Prerequisites
```bash
# System requirements
Python 3.9+
Node.js 18+
MongoDB Community 6.0+
```

#### Backend Setup
```bash
# Virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
pip install -r requirements.txt

# Environment configuration
cp .env.example .env
# Edit .env with your settings
```

#### Frontend Setup
```bash
cd frontend
npm install
npm run dev  # Development server
npm run build  # Production build
```

#### Database Setup
```bash
# MongoDB installation (Ubuntu)
sudo apt-get install mongodb-community

# Start MongoDB service
sudo systemctl start mongod
sudo systemctl enable mongod

# Create database and indexes
python scripts/init_db.py
```

## Technical Constraints

### Performance Requirements
- **API Response Time**: <400ms P95 on Raspberry Pi 4
- **Database Size**: <100MB with 10k bills, 50k todos
- **Memory Usage**: <512MB total application footprint
- **Network**: Minimal external API calls (weather only)

### Security Constraints
- **Authentication**: Session-based only, no JWT
- **Rate Limiting**: 5 login attempts per minute
- **CORS**: Single domain restriction
- **Input Validation**: All user inputs sanitized
- **Password Security**: PBKDF2 with 600k+ iterations

### Deployment Constraints
- **Single User**: No multi-tenancy support
- **Self-Hosted**: No cloud dependencies
- **Ubuntu Target**: Optimized for Ubuntu 20.04+
- **Cloudflare Tunnel**: HTTPS without port forwarding
- **systemd**: Service management integration

## Dependencies

### Backend Dependencies (requirements.txt)
```
Flask==3.0.0
Flask-Login==0.6.3
Flask-PyMongo==2.3.0
Flask-Limiter==3.5.0
python-decouple==3.8
python-dateutil==2.8.2
gunicorn==21.2.0
pymongo==4.6.0
requests==2.31.0
```

### Frontend Dependencies (package.json)
```json
{
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-grid-layout": "^1.4.4",
    "react-window": "^1.8.8",
    "@tanstack/react-query": "^5.0.0",
    "formik": "^2.4.5",
    "yup": "^1.3.3",
    "lucide-react": "^0.294.0",
    "tailwindcss": "^3.3.6"
  },
  "devDependencies": {
    "@vitejs/plugin-react": "^4.1.1",
    "vite": "^5.0.0"
  }
}
```

## API Design

### RESTful Endpoints
```
Authentication:
POST /api/auth/login
POST /api/auth/logout
GET  /api/auth/status

Dashboard:
GET  /api/dashboard

Bills & Vacations:
GET    /api/bills
POST   /api/bills
PATCH  /api/bills/:id
DELETE /api/bills/:id

Contacts:
GET    /api/contacts
POST   /api/contacts
PATCH  /api/contacts/:id
DELETE /api/contacts/:id

Weather:
GET    /api/weather?city=stl
GET    /api/weather?city=accra

Profile:
GET    /api/profile
PATCH  /api/profile

Layout:
POST   /api/layout
```

### Data Models
```python
# User model
{
    "_id": ObjectId,
    "username": str,
    "password_hash": str,
    "email": str (optional),
    "phone": str (optional),
    "dashboard_layout": dict,
    "categories": list,
    "created_at": datetime
}

# Bill model
{
    "_id": ObjectId,
    "user_id": ObjectId,
    "title": str,
    "due_date": datetime (optional),
    "rrule": str (optional),
    "amount": float (optional),
    "category": str (optional),
    "notes": str (optional),
    "created_at": datetime
}
```

## Environment Configuration

### Required Environment Variables
```
# Database
MONGODB_URI=mongodb://localhost:27017/personal_organizer

# Security
SECRET_KEY=your-secret-key-here
FLASK_ENV=production

# Weather API
WEATHER_API_BASE=https://api.open-meteo.com/v1

# Application
TIMEZONE=America/Chicago
DEBUG=False
```

## Monitoring and Logging
- **Application Logs**: Structured JSON logging
- **Error Tracking**: File-based error logs
- **Performance**: Response time logging
- **Security**: Failed login attempt logging
