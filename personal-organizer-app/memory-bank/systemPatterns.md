# System Patterns - Personal Organizer App

## Architecture Overview
The application follows a clean separation of concerns with a Flask API backend, MongoDB database, and React frontend. All components are designed for single-user deployment with production-grade security and performance.

## Key Technical Decisions

### 1. Authentication Pattern
- **Flask-Login**: Session-based authentication with secure cookies
- **Rate Limiting**: Flask-Limiter with 5 attempts per minute global rule
- **Security**: PBKDF2 password hashing with Werkzeug defaults (≥600k iterations)
- **Session Management**: Server-side session storage with automatic expiration

### 2. Data Storage Pattern
- **MongoDB**: Document-based storage for flexible schema evolution
- **TTL Indexes**: Automatic cleanup of weather cache (2h), old bill instances (24mo), completed todos (24h)
- **Hard Deletes**: Immediate removal for storage efficiency and privacy
- **UTC Storage**: All timestamps in UTC, converted to America/Chicago for display

### 3. Recurrence Engine Pattern
- **RRULE Standard**: RFC 5545 compliant recurrence rules using python-dateutil
- **Limited Expansion**: Only next 12 months materialized for performance
- **Separate Collections**: Bills and events in different collections for clean separation
- **Cascade Operations**: Master record changes propagate to materialized instances

### 4. Frontend Architecture Pattern
- **Component Hierarchy**: Pages → Widgets → Components → UI Elements
- **State Management**: React Query for server state, local state for UI
- **Grid System**: React-Grid-Layout for customizable dashboard
- **Virtualization**: react-window for large lists (>300 items)

## Design Patterns in Use

### 1. Repository Pattern (Backend)
```
models/
├── base.py          # Base model with common operations
├── user.py          # User model with authentication
├── bill.py          # Bill model with recurrence logic
├── contact.py       # Contact model with threshold calculations
└── weather.py       # Weather model with caching logic
```

### 2. Widget Pattern (Frontend)
```
components/widgets/
├── WeatherWidget.js     # Current conditions + forecast
├── BillsWidget.js       # Upcoming bills and vacations
├── ContactsWidget.js    # Color-coded contact status
├── TodosWidget.js       # Virtualized todo list
└── BaseWidget.js        # Common widget functionality
```

### 3. API Resource Pattern
```
routes/
├── auth.py          # Authentication endpoints
├── dashboard.py     # Aggregated dashboard data
├── bills.py         # CRUD operations for bills
├── contacts.py      # Contact management
├── weather.py       # Weather proxy with caching
└── profile.py       # User profile management
```

## Component Relationships

### Data Flow
1. **Frontend Request** → API Route → Model → MongoDB
2. **MongoDB Response** → Model → API Route → Frontend
3. **Cache Layer**: Weather data cached at API level
4. **Real-time Updates**: Optimistic UI updates with error rollback

### Security Layers
1. **Network**: Cloudflare Tunnel (HTTPS, no open ports)
2. **Application**: Flask-Limiter rate limiting
3. **Authentication**: Flask-Login session management
4. **Authorization**: @login_required decorators
5. **Data**: Input validation and sanitization

### Performance Optimizations
1. **Database**: TTL indexes for automatic cleanup
2. **API**: Weather caching with 2-hour expiration
3. **Frontend**: React Query with stale-while-revalidate
4. **UI**: Virtualization for large lists
5. **Network**: CORS optimization for single domain

## Error Handling Strategy

### Backend Error Handling
- **Validation Errors**: 400 with detailed field errors
- **Authentication Errors**: 401 with redirect to login
- **Authorization Errors**: 403 with access denied message
- **Not Found Errors**: 404 with resource not found
- **Server Errors**: 500 with generic error message (detailed logs)

### Frontend Error Handling
- **Network Errors**: Retry with exponential backoff
- **Validation Errors**: Inline form error display
- **Authentication Errors**: Automatic redirect to login
- **Loading States**: Skeleton screens and spinners
- **Fallback UI**: Error boundaries for component failures

## Deployment Architecture
```
Cloudflare Tunnel → Nginx → Gunicorn → Flask App
                                    ↓
                                 MongoDB
```

### Service Management
- **systemd**: Process management and auto-restart
- **Gunicorn**: WSGI server with multiple workers
- **MongoDB**: Community edition with replica set
- **Backup**: Weekly mongodump to secondary storage
